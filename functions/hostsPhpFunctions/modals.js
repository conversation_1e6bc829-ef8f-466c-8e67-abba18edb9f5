// Modal Handlers
const setupModals = () => {
    // Scan Modal
    const scanModal = document.getElementById("formModal");
    const scanBtn = document.getElementById("formModal-button");
    const scanClose = document.getElementsByClassName("formModal-close")[0];

    scanBtn.onclick = () => scanModal.classList.add("show");
    scanClose.onclick = () => scanModal.classList.remove("show");
    window.onclick = (event) => {
        if (event.target === scanModal) scanModal.classList.remove("show");
    };

    // Info Modal
    const infoModal = document.getElementById('infoModal');
    document.querySelector('.iframeMclose').onclick = () => {
        infoModal.classList.remove('show');
        infoModal.style.display = 'none';
    };
    window.addEventListener('click', (event) => {
        if (event.target === infoModal) {
            infoModal.classList.remove('show');
            infoModal.style.display = 'none';
        }
    });
};

const showModal = (url, modalType = null, serviceName = null, tab = null) => {
    const modal = document.getElementById('infoModal');
    const iframe = document.getElementById('modal-frame');
    const translator = new Translator(dictionaries, selectedLang);
    
    // Create loading animation
    let loader = document.getElementById('modal-loader');
    if (!loader) {
        loader = document.createElement('div');
        loader.id = 'modal-loader';
        loader.style.display = 'flex';
        loader.style.justifyContent = 'center';
        loader.style.alignItems = 'center';
        loader.style.position = 'absolute';
        loader.style.top = '50%';
        loader.style.left = '50%';
        loader.style.transform = 'translate(-50%, -50%)';
        loader.style.zIndex = '1000';
        
        // Add spinner element
        const spinner = document.createElement('div');
        spinner.style.border = '4px solid #f3f3f3';
        spinner.style.borderTop = '4px solid #3498db';
        spinner.style.borderRadius = '50%';
        spinner.style.width = '40px';
        spinner.style.height = '40px';
        spinner.style.animation = 'spin 2s linear infinite';
        loader.appendChild(spinner);
        
        // Add animation style if not already defined
        if (!document.getElementById('spinner-styles')) {
            const style = document.createElement('style');
            style.id = 'spinner-styles';
            style.textContent = `
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
        }
        
        // Add loader to modal
        modal.appendChild(loader);
    }

    if (url.includes('checkCommandHost.php')) {
        modalBody.style.maxHeight = '500px';
        modalBody.style.maxWidth = '500px';
        modalBody.style.top = 'calc(50vh - 250px)';
    } else if (url.includes('credentials.php')) {
        modalBody.style.maxHeight = '800px';
        modalBody.style.maxWidth = '1000px';
        modalBody.style.top = 'calc(50vh - 400px)';
    } else {
        // Explicitly reset all styles for non-special modals
        modalBody.style.maxHeight = '';
        modalBody.style.maxWidth = '';
        modalBody.style.top = '';
        modalBody.style.width = '';
        modalBody.style.height = '';
    }

    modal.classList.remove('loaded');
    iframe.style.display = 'none';
    // Show loader
    loader.style.display = 'flex';
    
    iframe.src = url;
    modal.style.display = 'block';
    modal.classList.add('show');
    modal.classList.remove('small');

    // Set data attributes for opening specific modals within the iframe
    if (modalType === 'host') {
        iframe.setAttribute('data-open-host-modal', 'true');
        if (tab) {
            iframe.setAttribute('data-open-tab', tab);
        }
    } else if (modalType === 'service' && serviceName) {
        iframe.setAttribute('data-open-service-modal', 'true');
        iframe.setAttribute('data-service-name', serviceName);
        if (tab) {
            iframe.setAttribute('data-open-tab', tab);
        }
    }

    iframe.onload = () => {
        // Hide loader when content is loaded
        loader.style.display = 'none';
        
        modal.classList.add('loaded');
        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
        const nav = iframeDoc.querySelector("nav");
        const header = iframeDoc.querySelector("header");

        if (nav) nav.style.display = "none";

        // Check if we need to open a host or service modal
        if (iframe.hasAttribute('data-open-host-modal')) {
            // Try to trigger a click on the host card to open host modal
            setTimeout(() => {
                try {
                    const hostCard = iframeDoc.getElementById('host-card');
                    if (hostCard) {
                        hostCard.click();
                        
                        // If we need to open a specific tab
                        const tab = iframe.getAttribute('data-open-tab');
                        if (tab === 'performance') {
                            // Wait for the modal to open and then click on the performance tab
                            setTimeout(() => {
                                try {
                                    const performanceTab = iframeDoc.querySelector('button.tab-button[data-tab="performance"]');
                                    if (performanceTab) {
                                        performanceTab.click();
                                    }
                                } catch (e) {
                                    console.error('Failed to open performance tab:', e);
                                }
                            }, 700);
                        }
                    }
                } catch (e) {
                    console.error('Failed to auto-open host modal:', e);
                }
                iframe.removeAttribute('data-open-host-modal');
                iframe.removeAttribute('data-open-tab');
            }, 500);
        } else if (iframe.hasAttribute('data-open-service-modal')) {
            // Try to trigger a click on the specific service card
            setTimeout(() => {
                try {
                    const serviceName = iframe.getAttribute('data-service-name');
                    const encodedServiceName = serviceName.replace(/\s+/g, '+');
                    const serviceCard = iframeDoc.querySelector(`.service-card[data-service="${encodedServiceName}"]`);
                    if (serviceCard) {
                        serviceCard.click();
                        
                        // If we need to open a specific tab
                        const tab = iframe.getAttribute('data-open-tab');
                        if (tab === 'performance') {
                            // Wait for the modal to open and then click on the performance tab
                            setTimeout(() => {
                                try {
                                    const performanceTab = iframeDoc.querySelector('button.tab-button[data-tab="performance"]');
                                    if (performanceTab) {
                                        performanceTab.click();
                                    }
                                } catch (e) {
                                    console.error('Failed to open performance tab:', e);
                                }
                            }, 700);
                        }
                    }
                } catch (e) {
                    console.error('Failed to auto-open service modal:', e);
                }
                iframe.removeAttribute('data-open-service-modal');
                iframe.removeAttribute('data-service-name');
                iframe.removeAttribute('data-open-tab');
            }, 500);
        }

        // Apply theme-based styling for cmd.cgi pages
        if (url.includes('cmd.cgi')) {
            try {
                // Get current theme from parent document
                getCurrentTheme().then(theme => {
                    // Inject custom CSS based on theme
                    const style = iframeDoc.createElement('style');
                    style.textContent = theme === 'light-theme' ? 
                        getLightThemeStyles() : getDarkThemeStyles();
                    
                    iframeDoc.head.appendChild(style);

                    // Hide alert elements if they exist
                    const alertElement = iframeDoc.querySelector('.alert.alert-warning.alert-dismissible');
                    if (alertElement) {
                        alertElement.dataset.originalDisplay = alertElement.style.display || 'block';
                        alertElement.style.display = 'none';
                    }
                });
            } catch (error) {
                console.warn("Cannot access iframe content due to cross-origin restrictions:", error);
            }
        }

        // Handle form submissions to reload parent page after redirect
        const forms = iframeDoc.getElementsByTagName('form');
        if (!url.includes('credentials.php')) {
            Array.from(forms).forEach(form => {
                form.addEventListener('submit', () => {
                    // Set a flag to reload parent after redirect completes
                    sessionStorage.setItem('shouldReloadParent', 'true');
                });
            });
        }

        // Check if we should reload parent
        if (sessionStorage.getItem('shouldReloadParent')) {
            sessionStorage.removeItem('shouldReloadParent');
            window.parent.location.reload();
        }

        translator.translateIframe(iframe);
        iframe.style.display = 'block';
    };
};

// Make showModal globally accessible
window.showModal = showModal;

// Helper functions for theme styles
// Get current theme from document
async function getCurrentTheme() {
    try {
        // Check if there's a link element with href containing the theme name
        const themeLinks = document.querySelectorAll('link[rel="stylesheet"]');
        for (const link of themeLinks) {
            if (link.href.includes('/styles/')) {
                const themePath = link.href;
                // Extract theme name from path like "styles/light-theme/style.css"
                const themeMatch = themePath.match(/\/styles\/([^\/]+)\//);
                if (themeMatch && themeMatch[1]) {
                    return themeMatch[1];
                }
            }
        }
        // Fallback: check if body has a class indicating the theme
        const bodyClasses = document.body.className;
        if (bodyClasses.includes('light-theme')) return 'light-theme';
        if (bodyClasses.includes('dark-theme')) return 'dark-theme';
        
        return 'dark-theme';
    } catch (error) {
        console.error('Error detecting theme:', error);
        return 'dark-theme';
    }
}

// Light theme styles
function getLightThemeStyles() {
    return `
        body {
            font-family: 'Calibri', sans-serif;
            color: #333;
            background-color: #f4f4f4;
            line-height: 1.2;
            font-size: 13px;
            margin: 0;
            padding: 0;
        }
        
        table[width="90%"] {
            width: 100% !important;
            border-collapse: separate;
            border-spacing: 0;
            margin-top: 6px;
            border-radius: 3px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
        }
        
        table[width="90%"] > tbody > tr > td {
            display: block;
            width: 100% !important;
            padding: 6px;
            border-bottom: 1px solid #B0B0B0;
        }
        
        .commandDescription {
            background-color: #f4f4f4;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #B0B0B0;
            margin-bottom: 6px;
        }
        
        .jumbotron {
            margin: 0 0 8px 0;
            padding: 8px;
            border-radius: 4px;
        }
        
        .optBox {
            padding: 8px;
        }
        
        .optBoxItem {
            padding: 0px;    
        }
        
        .optBox input[type="text"].form-control {
            padding: 4px;
            margin: 4px 0;
            height: 28px;
        }
        
        input[type="submit"].btn-warning, 
        input[type="reset"].btn-warning,
        .btn.btn-warning {
            background-color: #f0f0f0 !important;
            color: #333;
            border: 1px solid #cbd5e1 !important;
            border-radius: 4px;
            padding: 4px 12px;
            min-width: 70px;
            height: 28px;
            margin: 6px 3px;
            font-size: 11px;
        }

        input[type="submit"].btn-warning:hover, 
        input[type="reset"].btn-warning:hover,
        .btn.btn-warning:hover {
            background-color: #e0e0e0 !important;
            transform: translateY(-1px);
        }
        .optBoxTitle, .descriptionTitle {
            margin-bottom: 6px;
            font-size: 14px;
            padding-bottom: 4px;
        }
        
        select {
            padding: 4px;
            height: 28px;
        }
        
        label {
            margin-bottom: 3px;
            font-size: 12px;
        }
    `;
}

// Dark theme styles
function getDarkThemeStyles() {
    return `
        body {
            font-family: 'Calibri', sans-serif;
            color: #fff;
            background-color: #2f2f2f;
            line-height: 1.2;
            font-size: 13px;
            margin: 0;
            padding: 0;
        }
        
        table[width="90%"] {
            width: 100% !important;
            border-collapse: separate;
            border-spacing: 0;
            margin-top: 6px;
            border-radius: 3px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
        }
        
        table[width="90%"] > tbody > tr > td {
            display: block;
            width: 100% !important;
            padding: 6px;
            border-bottom: 1px solid #444;
        }
        
        .commandDescription {
            background-color: #222;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #444;
            margin-bottom: 6px;
        }
        
        .jumbotron {
            background-color: #1f1f1f;
            margin: 0 0 8px 0;
            padding: 8px;
            border-radius: 4px;
        }
        
        .optBox {
            padding: 8px;
        }
        
        .optBoxItem {
            padding: 0px;
        }
        
        .optBox input[type="text"].form-control {
            padding: 4px;
            margin: 4px 0;
            height: 28px;
        }
        
        input[type="submit"].btn-warning, 
        input[type="reset"].btn-warning,
        .btn.btn-warning {
            background-color: #333 !important;
            color: #fff;
            border: 1px solid #888 !important;
            border-radius: 4px;
            padding: 4px 12px;
            min-width: 70px;
            height: 28px;
            margin: 6px 3px;
            font-size: 11px;
        }

        input[type="submit"].btn-warning:hover, 
        input[type="reset"].btn-warning:hover,
        .btn.btn-warning:hover {
            background-color: #444 !important;
            transform: translateY(-1px);
        }
        
        .optBoxTitle, .descriptionTitle {
            margin-bottom: 6px;
            font-size: 14px;
            padding-bottom: 4px;
        }
        
        select {
            padding: 4px;
            height: 28px;
        }
        
        label {
            margin-bottom: 3px;
            font-size: 12px;
        }
    `;
}

// Form Handling - Now handled by modular scanModal.js