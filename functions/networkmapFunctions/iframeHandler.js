/**
 * <PERSON><PERSON><PERSON> Handler for Network Map
 * Handles style injections to hide unwanted elements in iframes
 */

class IframeHandler {
    constructor() {
        this.init();
    }

    init() {
        // Handle main networkmap iframe
        this.handleMainIframe();
        
        // Handle modal iframes
        this.handleModalIframes();
    }

    handleMainIframe() {
        const networkmapIframe = document.querySelector('.networkmap-iframe');
        if (networkmapIframe) {
            networkmapIframe.addEventListener('load', () => {
                this.injectStyles(networkmapIframe);
            });
        }
    }

    handleModalIframes() {
        // Watch for modal iframe creation and handle existing ones
        const modalIframe = document.getElementById('modal-frame');
        if (modalIframe) {
            modalIframe.addEventListener('load', () => {
                this.injectStyles(modalIframe);
            });
        }

        // Also handle the showModal function override
        this.overrideShowModal();
    }

    overrideShowModal() {
        // Store the original showModal function
        const originalShowModal = window.showModal;
        
        if (originalShowModal) {
            window.showModal = function(url) {
                // Call the original function
                originalShowModal(url);
                
                // Get the modal iframe and add our handler
                const modalIframe = document.getElementById('modal-frame');
                if (modalIframe) {
                    modalIframe.addEventListener('load', () => {
                        // Use setTimeout to ensure the iframe is fully loaded
                        setTimeout(() => {
                            this.injectStyles(modalIframe);
                        }, 100);
                    });
                }
            }.bind(this);
        }
    }

    injectStyles(iframe) {
        try {
            const iframeDocument = iframe.contentDocument || iframe.contentWindow.document;
            
            if (iframeDocument) {
                // Create style element if it doesn't exist
                let styleElement = iframeDocument.getElementById('bubblemaps-injected-styles');
                
                if (!styleElement) {
                    styleElement = iframeDocument.createElement('style');
                    styleElement.id = 'bubblemaps-injected-styles';
                    iframeDocument.head.appendChild(styleElement);
                }

                // Add CSS to hide navbar and style the header
                const cssRules = `
                    /* Hide the navbar */
                    nav.navbar {
                        display: none !important;
                    }
                    
                    /* Style the header to match our theme */
                    #header {
                        background: #403c3c !important;
                        border-bottom: 1px solid #555 !important;
                        color: #ffffff !important;
                    }
                    
                    #header .head li {
                        color: #ffffff !important;
                    }
                    
                    #header .head a {
                        color: #ffffff !important;
                    }
                    
                    #header .dropdown span {
                        color: #ffffff !important;
                    }
                `;

                styleElement.textContent = cssRules;
                
                console.log('IframeHandler: Styles injected successfully');
            }
        } catch (error) {
            console.warn('IframeHandler: Could not inject styles into iframe (CORS or other restriction):', error);
        }
    }
}

// Initialize the iframe handler when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new IframeHandler();
});

// Also initialize immediately if DOM is already loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        new IframeHandler();
    });
} else {
    new IframeHandler();
}
