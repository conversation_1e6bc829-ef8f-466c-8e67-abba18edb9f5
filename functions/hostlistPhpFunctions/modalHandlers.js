/**
 * Modal functionality for Hostlist view
 * Handles opening and closing modals for hosts and services
 */

/**
 * Show modal function for credentials and other content
 * @param {string} url - URL to load in the iframe
 * @param {string|null} modalType - Type of modal to open (host, service, null)
 * @param {string|null} serviceName - Name of service if modalType is service
 * @param {string|null} tab - Tab to open (performance, null)
 */
function showModal(url, modalType = null, serviceName = null, tab = null) {
    const modal = document.getElementById('infoModal');
    const iframe = document.getElementById('modal-frame');
    const modalBody = document.getElementById('iframeModal-content');

    if (url.includes('credentials.php')){
        modalBody.style.maxHeight = '800px';
        modalBody.style.maxWidth = '1000px';
        modalBody.style.top = 'calc(50vh - 400px)';
    } else {
        // Explicitly reset all styles for non-credentials modals
        modalBody.style.maxHeight = '';
        modalBody.style.maxWidth = '';
        modalBody.style.top = '';
        modalBody.style.width = '';
        modalBody.style.height = '';
    }

    modal.classList.remove('loaded');
    iframe.style.display = 'none';
    iframe.src = url;
    modal.style.display = 'block';
    modal.classList.add('show');
    modal.classList.remove('small');

    // Set data attributes for opening specific modals within the iframe
    if (modalType === 'host') {
        iframe.setAttribute('data-open-host-modal', 'true');
        if (tab) {
            iframe.setAttribute('data-open-tab', tab);
        }
    } else if (modalType === 'service' && serviceName) {
        iframe.setAttribute('data-open-service-modal', 'true');
        iframe.setAttribute('data-service-name', serviceName);
        if (tab) {
            iframe.setAttribute('data-open-tab', tab);
        }
    }

    iframe.onload = function() {
        const iframeDocument = iframe.contentDocument || iframe.contentWindow.document;
        const navElement = iframeDocument.querySelector("nav");
        if (navElement) {
            navElement.style.display = "none";
        }
        
        // Check if we need to open a host or service modal
        if (iframe.hasAttribute('data-open-host-modal')) {
            // Try to trigger a click on the host card to open host modal
            setTimeout(() => {
                try {
                    const hostCard = iframeDocument.getElementById('host-card');
                    if (hostCard) {
                        hostCard.click();
                        
                        // If we need to open a specific tab
                        const tab = iframe.getAttribute('data-open-tab');
                        if (tab === 'performance') {
                            // Wait for the modal to open and then click on the performance tab
                            setTimeout(() => {
                                try {
                                    // Find and click the performance tab button
                                    const performanceTab = iframeDocument.querySelector('button.tab-button[data-tab="performance"]');
                                    if (performanceTab) {
                                        performanceTab.click();
                                    } else {
                                        console.error('Performance tab button not found');
                                    }
                                } catch (e) {
                                    console.error('Failed to open performance tab:', e);
                                }
                            }, 700);
                        }
                    }
                } catch (e) {
                    console.error('Failed to auto-open host modal:', e);
                }
                iframe.removeAttribute('data-open-host-modal');
                iframe.removeAttribute('data-open-tab');
            }, 500);
        } else if (iframe.hasAttribute('data-open-service-modal')) {
            // Try to trigger a click on the specific service card
            setTimeout(() => {
                try {
                    const serviceName = iframe.getAttribute('data-service-name');
                    const encodedServiceName = encodeURIComponent(serviceName);
                    const serviceCard = iframeDocument.querySelector(`.service-card[data-service="${encodedServiceName}"]`);
                    if (serviceCard) {
                        serviceCard.click();
                        
                        // If we need to open a specific tab
                        const tab = iframe.getAttribute('data-open-tab');
                        if (tab === 'performance') {
                            // Wait for the modal to open and then click on the performance tab
                            setTimeout(() => {
                                try {
                                    // Find and click the performance tab button
                                    const performanceTab = iframeDocument.querySelector('button.tab-button[data-tab="performance"]');
                                    if (performanceTab) {
                                        performanceTab.click();
                                    } else {
                                        console.error('Performance tab button not found');
                                    }
                                } catch (e) {
                                    console.error('Failed to open performance tab:', e);
                                }
                            }, 700);
                        }
                    }
                } catch (e) {
                    console.error('Failed to auto-open service modal:', e);
                }
                iframe.removeAttribute('data-open-service-modal');
                iframe.removeAttribute('data-service-name');
                iframe.removeAttribute('data-open-tab');
            }, 500);
        }
        
        // Translate the iframe content
        if (typeof translator !== 'undefined') {
            translator.translateIframe(iframe);
        }
        modal.classList.add('loaded');
        iframe.style.display = 'block';
    };
}

/**
 * Open a host in the modal iframe and trigger the host modal to open
 * @param {String} hostname - The hostname 
 * @param {String} ip - The IP address
 * @param {String} subnet - The subnet
 * @param {Boolean} openHostModal - Whether to open the host modal within the iframe
 * @param {String} tab - The tab to open (null, 'performance', etc.)
 */
function openHostInModal(hostname, ip, subnet, openHostModal = false, tab = null) {
    // Determine Bubble View nickname if available
    let nicknameToUse = hostname;
    if (typeof bubbleHostnamesMap !== 'undefined' && bubbleHostnamesMap && bubbleHostnamesMap[ip]) {
        nicknameToUse = bubbleHostnamesMap[ip];
    }
    // Use the same modal opening approach as in fetchHostsBubbles.js
    const urlParams = new URLSearchParams(window.location.search);
    const infraParam = urlParams.get('infra');
    const url = `host.php?nickname=${encodeURIComponent(nicknameToUse)}&ip=${encodeURIComponent(hostname)}&infra=${encodeURIComponent(infraParam)}&hostip=${encodeURIComponent(ip)}&subnet=${encodeURIComponent(subnet || 'External')}`;
    
    // Use the showModal function if it exists, otherwise fall back to window.location
    if (typeof showModal === 'function') {
        showModal(url, openHostModal ? 'host' : null, null, tab);
    } else {
        // Fallback implementation of showModal
        const modal = document.getElementById('infoModal');
        const iframe = document.getElementById('modal-frame') || document.getElementById('iframeModal-frame');
        
        if (modal && iframe) {
            iframe.src = url;
            modal.style.display = 'block';
            modal.classList.add('show');
            
            if (openHostModal) {
                // Set a flag to open the host modal when iframe loads
                iframe.setAttribute('data-open-host-modal', 'true');
                if (tab) {
                    iframe.setAttribute('data-open-tab', tab);
                }
            }
        } else {
            window.location.href = url;
        }
    }
}

/**
 * Open a service in the modal iframe and trigger the service modal to open
 * @param {String} hostname - The hostname 
 * @param {String} ip - The IP address
 * @param {String} subnet - The subnet
 * @param {String} serviceName - The service name to open
 * @param {String} tab - The tab to open (null, 'performance', etc.)
 */
function openServiceInModal(hostname, ip, subnet, serviceName, tab = null) {
    // Determine Bubble View nickname if available
    let nicknameToUse = hostname;
    if (typeof bubbleHostnamesMap !== 'undefined' && bubbleHostnamesMap && bubbleHostnamesMap[ip]) {
        nicknameToUse = bubbleHostnamesMap[ip];
    }
    // Use the same modal opening approach as in fetchHostsBubbles.js
    const urlParams = new URLSearchParams(window.location.search);
    const infraParam = urlParams.get('infra');
    const url = `host.php?nickname=${encodeURIComponent(nicknameToUse)}&ip=${encodeURIComponent(hostname)}&infra=${encodeURIComponent(infraParam)}&hostip=${encodeURIComponent(ip)}&subnet=${encodeURIComponent(subnet || 'External')}`;
    
    // Use the showModal function if it exists, otherwise fall back to window.location
    if (typeof showModal === 'function') {
        showModal(url, 'service', serviceName, tab);
    } else {
        // Fallback implementation of showModal
        const modal = document.getElementById('infoModal');
        const iframe = document.getElementById('modal-frame') || document.getElementById('iframeModal-frame');
        
        if (modal && iframe) {
            iframe.src = url;
            modal.style.display = 'block';
            modal.classList.add('show');
            
            // Set data attributes to open the service modal when iframe loads
            iframe.setAttribute('data-open-service-modal', 'true');
            iframe.setAttribute('data-service-name', serviceName);
            if (tab) {
                iframe.setAttribute('data-open-tab', tab);
            }
        } else {
            window.location.href = url;
        }
    }
}

/**
 * Setup modal event handlers
 */
function setupModalEventHandlers() {
    // Close iframe modal when clicking the close button
    const closeButton = document.querySelector('.iframeMclose');
    if (closeButton) {
        closeButton.addEventListener('click', function() {
            const modal = document.getElementById('infoModal');
            modal.classList.remove('show');
            modal.style.display = 'none';
            
            // Reset any iframe data attributes
            const iframe = document.getElementById('modal-frame');
            if (iframe) {
                iframe.removeAttribute('data-open-host-modal');
                iframe.removeAttribute('data-open-service-modal');
                iframe.removeAttribute('data-service-name');
                iframe.removeAttribute('data-open-tab');
            }
        });
    }
    
    // Close modal when clicking outside
    window.addEventListener('click', function(event) {
        const modal = document.getElementById('infoModal');
        if (event.target == modal) {
            modal.classList.remove('show');
            modal.style.display = 'none';
            
            // Reset any iframe data attributes
            const iframe = document.getElementById('modal-frame');
            if (iframe) {
                iframe.removeAttribute('data-open-host-modal');
                iframe.removeAttribute('data-open-service-modal');
                iframe.removeAttribute('data-service-name');
                iframe.removeAttribute('data-open-tab');
            }
        }
    });
}

// Export functions to global scope for use in other pages
window.showModal = showModal;
window.openHostInModal = openHostInModal;
window.openServiceInModal = openServiceInModal;
window.setupModalEventHandlers = setupModalEventHandlers; 