// SPM (Switch Port Manager) Handler
// Handles fetching and displaying network connectivity information from Netdisco

// Function to fetch SPM data for a host
async function fetchSpmData(hostIp) {
    try {
        const response = await fetch(`get_spm_data.php?ip=${encodeURIComponent(hostIp)}`);
        
        // Handle 500 status code specifically
        if (response.status === 500) {
            const errorData = await response.json();
            return {
                success: false,
                error: errorData.error || 'SPM module is not available',
                parent_connections: [],
                child_connections: []
            };
        }
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.error) {
            throw new Error(data.error);
        }
        
        return data;
    } catch (error) {
        console.error('Error fetching SPM data:', error);
        return {
            success: false,
            error: error.message,
            parent_connections: [],
            child_connections: []
        };
    }
}

// Function to format port status with icons and colors
function formatPortStatus(status) {
    if (status === 'Up') {
        return '<span class="spm-status-badge spm-status-up"><i class="fa fa-arrow-up"></i></span>';
    } else {
        return '<span class="spm-status-badge spm-status-down"><i class="fa fa-arrow-down"></i></span>';
    }
}

/* --- Connectivity Host Linking Enhancements --- */
// Cache of bubblemaps hostnames indexed by IP
let bubbleHostnamesMap = null;
// Cache of Nagios hostnames indexed by IP
let nagiosHostnamesMap = null;

/**
 * Fetch bubblemaps hostnames map (IP => nickname) once per page load.
 */
async function fetchBubbleHostnames() {
    if (bubbleHostnamesMap !== null) {
        return bubbleHostnamesMap;
    }
    try {
        const resp = await fetch('get_bubble_hostnames.php');
        if (resp.ok) {
            bubbleHostnamesMap = await resp.json();
        } else {
            bubbleHostnamesMap = {};
        }
    } catch (e) {
        console.error('Failed loading bubblemaps hostnames:', e);
        bubbleHostnamesMap = {};
    }
    return bubbleHostnamesMap;
}

/**
 * Fetch all Nagios hosts once per page load.
 */
async function fetchNagiosHostnames() {
    if (nagiosHostnamesMap !== null) return nagiosHostnamesMap;
    nagiosHostnamesMap = {};
    try {
        const url = `https://${window.location.hostname}/nagios/cgi-bin/objectjson.cgi?query=hostlist&details=true`;
        const resp = await fetch(url);
        if (resp.ok) {
            const data = await resp.json();
            for (const hn in data.data.hostlist) {
                const host = data.data.hostlist[hn];
                nagiosHostnamesMap[host.address] = host.name;
            }
        }
    } catch (e) {
        console.warn('Failed to preload Nagios hostlist', e);
    }
    return nagiosHostnamesMap;
}

/**
 * Generate a host.php link for the given IP if the IP exists in bubblemaps.
 * Otherwise returns the plain IP string.
 */
function generateHostLink(ipString) {
    if (!ipString) return '';
    // Split in case multiple IPs separated by comma / semicolon / whitespace
    const ipParts = ipString.split(/[,;\s]+/).filter(Boolean);
    const linkedParts = ipParts.map(rawIp => {
        const cleanIp = rawIp.trim();
        if (cleanIp.toLowerCase() === 'n/a') return cleanIp;
        if (nagiosHostnamesMap && nagiosHostnamesMap[cleanIp]) {
            return `<a style="color: #4a9eff;" href="#" data-hostip="${cleanIp}" onclick="openHostFromConnectivity(event, '${cleanIp}')">${cleanIp}</a>`;
        }
        return cleanIp;
    });
    return linkedParts.join(', ');
}

// Opens host.php for the given IP, resolving Nagios hostname first
async function openHostFromConnectivity(event, ip) {
    if (event) event.preventDefault();
    await fetchBubbleHostnames();
    const nickname = (bubbleHostnamesMap && bubbleHostnamesMap[ip]) ? bubbleHostnamesMap[ip] : ip;
    
    // Get the real Nagios hostname
    let realHostName = null;
    if (typeof getHostnameByIP === 'function') {
        try {
            realHostName = await getHostnameByIP(ip);
        } catch (e) {
            console.warn('Hostname lookup failed', e);
        }
    }
    
    if (!realHostName) {
        alert('Host not found in Nagios yet. Please wait until it is added.');
        return;
    }
    
    const url = `host.php?nickname=${encodeURIComponent(nickname)}&ip=${encodeURIComponent(realHostName)}&infra=null&hostip=${encodeURIComponent(ip)}&subnet=all`;
    window.location.href = url;
}
/* --- End Connectivity Host Linking Enhancements --- */

// Function to create SPM table HTML
function createSpmTable(connections, title, emptyMessage, isChildConnections = false) {
    if (!connections || connections.length === 0) {
        return `
            <div class="spm-section">
                <h3>${title}</h3>
                <div class="spm-empty">
                    <i class="fa fa-info-circle"></i>
                    <p>${emptyMessage}</p>
                </div>
            </div>
        `;
    }
    
    // Sort connections alphabetically by port
    connections.sort((a, b) => {
        const portA = a.port || '';
        const portB = b.port || '';
        return portA.localeCompare(portB, undefined, { numeric: true, sensitivity: 'base' });
    });
    
    let tableHTML = `
        <div class="spm-section">
            <h3>${title}</h3>
            <div class="spm-table-container">
                <table class="spm-table">
                    <thead>
                        <tr>
                            <th>Status</th>
                            ${isChildConnections ? '<th>Switch IP</th><th>Switch Name</th>' : ''}
                            <th>Port</th>
                            <th>Description</th>
                            <th>Speed</th>
                            <th>Native VLAN</th>
                            <th>VLAN Membership</th>
                            ${isChildConnections ? '' : '<th>Device IP</th>'}
                            <th>Type</th>
                            <th>Last Seen</th>
                        </tr>
                    </thead>
                    <tbody>
    `;
    
    connections.forEach(conn => {
        const lastSeen = conn.last_seen ? new Date(conn.last_seen).toLocaleString() : 'N/A';
        const deviceIp = conn.child_ip || 'N/A';
        const connectionType = conn.connection_type === 'Unknown' ? 'N/A' : conn.connection_type;
        
        if (isChildConnections) {
            // For child connections, show the switch (parent) information
            tableHTML += `
                <tr>
                    <td>${formatPortStatus(conn.port_status)}</td>
                    <td>${generateHostLink(conn.parent_ip)}</td>
                    <td>${conn.parent_name}</td>
                    <td>${conn.port}</td>
                    <td>${conn.port_description || '-'}</td>
                    <td>${conn.port_speed}</td>
                    <td>${conn.native_vlan || '-'}</td>
                    <td>${conn.vlan_membership || '-'}</td>
                    <td><span class="spm-type-badge spm-type-${connectionType.toLowerCase()}">${connectionType}</span></td>
                    <td>${lastSeen}</td>
                </tr>
            `;
        } else {
            // For parent connections, show the connected device information (removed Connected Device column)
            tableHTML += `
                <tr>
                    <td>${formatPortStatus(conn.port_status)}</td>
                    <td>${conn.port}</td>
                    <td>${conn.port_description || '-'}</td>
                    <td>${conn.port_speed}</td>
                    <td>${conn.native_vlan || '-'}</td>
                    <td>${conn.vlan_membership || '-'}</td>
                    <td>${generateHostLink(deviceIp)}</td>
                    <td><span class="spm-type-badge spm-type-${connectionType.toLowerCase()}">${connectionType}</span></td>
                    <td>${lastSeen}</td>
                </tr>
            `;
        }
    });
    
    tableHTML += `
                    </tbody>
                </table>
            </div>
        </div>
    `;
    
    return tableHTML;
}

// Function to load and display SPM data
async function loadSpmData(hostIp) {
    const spmContainer = document.getElementById('spm-container');
    if (!spmContainer) return;
    
    // Show loading state
    spmContainer.innerHTML = `
        <div class="spm-loading">
            <i class="fa fa-spinner fa-spin"></i>
            <p>Loading network connectivity data...</p>
        </div>
    `;
    
    try {
        // Preload host maps (Bubblemaps + Nagios) first
        await fetchBubbleHostnames();
        await fetchNagiosHostnames();
        const data = await fetchSpmData(hostIp);
        
        if (!data.success) {
            // Check if the error is due to SPM module not being available
            if (data.error && data.error.includes('SPM module is not available')) {
                // Show marketing message instead of hiding the container
                spmContainer.innerHTML = `
                    <div class="spm-header">
                        <h2><i class="fa fa-network-wired"></i> Connectivity information</h2>
                    </div>
                    <div class="spm-marketing">
                        <i class="fa fa-info-circle"></i>
                        <p><strong>Switch Port Manager module</strong> is required to view network connectivity information.</p>
                        <p>Upgrade your license to access switch port mappings and device connectivity details.</p>
                    </div>
                `;
                return;
            }
            
            spmContainer.innerHTML = `
                <div class="spm-error">
                    <i class="fa fa-exclamation-triangle"></i>
                    <p>Error loading SPM data: ${data.error}</p>
                </div>
            `;
            return;
        }
        
        // Create the SPM content
        let spmHTML = `
            <div class="spm-header">
                <h2><i class="fa fa-network-wired"></i> Connectivity information</h2>
            </div>
        `;
        
        // Add parent connections (this host as a switch) - only if there are connections
        if (data.parent_connections && data.parent_connections.length > 0) {
            spmHTML += createSpmTable(
                data.parent_connections,
                'Devices connected via this host (Switch)',
                'No devices connected to this host',
                false // isChildConnections = false
            );
        }
        
        // Add child connections (this host as a connected device) - only if there are connections
        if (data.child_connections && data.child_connections.length > 0) {
            spmHTML += createSpmTable(
                data.child_connections,
                'Switch interfaces linked to this host',
                'No switch port connections found for this host',
                true // isChildConnections = true
            );
        }
        
        // If no connections at all, show a message
        if ((!data.parent_connections || data.parent_connections.length === 0) && 
            (!data.child_connections || data.child_connections.length === 0)) {
            spmHTML += `
                <div class="spm-empty">
                    <i class="fa fa-info-circle"></i>
                    <p>No network connectivity information available for this host.</p>
                </div>
            `;
        }
        
        spmContainer.innerHTML = spmHTML;
        
        // Apply any existing search filter to the newly loaded SPM data
        if (typeof window.applyAllFilters === 'function') {
            // Small delay to ensure DOM is updated
            setTimeout(() => {
                window.applyAllFilters();
            }, 100);
        }
        
        // Trigger search initialization in case SPM data loaded before services
        // This will show the search bar if SPM tables are present and update placeholder text
        if (typeof window.initSearchFunctionality === 'function') {
            setTimeout(() => {
                window.initSearchFunctionality();
            }, 150);
        }
        
    } catch (error) {
        console.error('Error in loadSpmData:', error);
        spmContainer.innerHTML = `
            <div class="spm-error">
                <i class="fa fa-exclamation-triangle"></i>
                <p>Failed to load SPM data: ${error.message}</p>
            </div>
        `;
    }
}

// Function to initialize SPM functionality
async function initSpm() {
    const spmContainer = document.getElementById('spm-container');
    if (!spmContainer) return;
    
    // Get the host IP from the URL parameter 'hostip' which contains the actual IP
    const hostIp = new URLSearchParams(window.location.search).get('hostip');
    if (!hostIp) {
        // Fallback to 'ip' parameter if 'hostip' is not available
        const fallbackIp = new URLSearchParams(window.location.search).get('ip');
        if (!fallbackIp) return;
        
        // Check module availability first before showing loading
        try {
            const response = await fetch(`get_spm_data.php?ip=${encodeURIComponent(fallbackIp)}`);
            if (response.status === 500) {
                const errorData = await response.json();
                if (errorData.error && errorData.error.includes('SPM module is not available')) {
                    // Show marketing message instead of hiding the container
                    spmContainer.style.display = 'block';
                    spmContainer.innerHTML = `
                        <div class="spm-header">
                            <h2><i class="fa fa-network-wired"></i> Connectivity information</h2>
                        </div>
                        <div class="spm-marketing">
                            <i class="fa fa-info-circle"></i>
                            <p><strong>Switch Port Manager module</strong> is required to view network connectivity information.</p>
                            <p>Upgrade your license to access switch port mappings and device connectivity details.</p>
                        </div>
                    `;
                    return;
                }
            }
            // Module is available, show the container and load data
            spmContainer.style.display = 'block';
            loadSpmData(fallbackIp);
        } catch (error) {
            // If we can't check availability, show container and proceed with loading
            spmContainer.style.display = 'block';
            loadSpmData(fallbackIp);
        }
        return;
    }
    
    // Check module availability first before showing loading
    try {
        const response = await fetch(`get_spm_data.php?ip=${encodeURIComponent(hostIp)}`);
        if (response.status === 500) {
            const errorData = await response.json();
            if (errorData.error && errorData.error.includes('SPM module is not available')) {
                // Show marketing message instead of hiding the container
                spmContainer.style.display = 'block';
                spmContainer.innerHTML = `
                    <div class="spm-header">
                        <h2><i class="fa fa-network-wired"></i> Connectivity information</h2>
                    </div>
                    <div class="spm-marketing">
                        <i class="fa fa-info-circle"></i>
                        <p><strong>Switch Port Manager module</strong> is required to view network connectivity information.</p>
                        <p>Upgrade your license to access switch port mappings and device connectivity details.</p>
                    </div>
                `;
                return;
            }
        }
        // Module is available, show the container and load data
        spmContainer.style.display = 'block';
        loadSpmData(hostIp);
    } catch (error) {
        // If we can't check availability, show container and proceed with loading
        spmContainer.style.display = 'block';
        loadSpmData(hostIp);
    }
}

// Export functions for global access
window.loadSpmData = loadSpmData;
window.initSpm = initSpm; 