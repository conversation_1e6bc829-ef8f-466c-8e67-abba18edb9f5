// Toggle between card (grid) view and list (table) view for services in hostApm page

(function () {
    document.addEventListener('DOMContentLoaded', () => {
        const toggleBtn = document.getElementById('view-toggle-button');
        if (!toggleBtn) return;

        let isListView = false;
        let savedViewType = 'card'; // Default fallback

        // Function to load view type from server
        async function loadViewType() {
            try {
                const response = await fetch('configHandler.php');
                const data = await response.json();
                if (data.success) {
                    savedViewType = data.viewType;
                    return data.viewType;
                }
            } catch (error) {
                console.error('Error loading view type:', error);
            }
            return 'card'; // Default fallback
        }

        // Function to save view type to server
        async function saveViewType(viewType) {
            try {
                const response = await fetch('configHandler.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ viewType })
                });
                const data = await response.json();
                return data.success;
            } catch (error) {
                console.error('Error saving view type:', error);
                return false;
            }
        }

        // Function to apply view type
        function applyViewType(viewType) {
            const servicesGrid = document.querySelector('.services-grid');
            const statusContainer = document.getElementById('status');
            if (!servicesGrid || !statusContainer) {
                // Services not loaded yet, store the preference and try again later
                savedViewType = viewType;
                return false;
            }

            let listTable = document.getElementById('services-list-table');

            if (viewType === 'list') {
                // Switch to list view
                if (!listTable) {
                    listTable = buildTableFromCards();
                    statusContainer.appendChild(listTable);
                    // ensure filters/search reflect new table
                    if (typeof applyAllFilters === 'function') {
                        setTimeout(() => applyAllFilters(), 50);
                    }
                }
                servicesGrid.style.display = 'none';
                listTable.style.display = 'table';
                toggleBtn.innerHTML = '<i class="fa fa-list"></i>';
                toggleBtn.title = 'Switch to card view';
                isListView = true;
            } else {
                // Switch to card view
                servicesGrid.style.display = 'grid';
                if (listTable) listTable.style.display = 'none';
                toggleBtn.innerHTML = '<i class="fa fa-th-large"></i>';
                toggleBtn.title = 'Switch to list view';
                isListView = false;
                if (typeof applyAllFilters === 'function') {
                    setTimeout(() => applyAllFilters(), 50);
                }
            }
            return true;
        }

        // Function to try applying view type when services are available
        function tryApplyViewType() {
            if (savedViewType && applyViewType(savedViewType)) {
                // Successfully applied, clear the saved preference
                savedViewType = null;
            }
        }

        // Initialize view type on page load
        loadViewType().then(viewType => {
            if (!applyViewType(viewType)) {
                // Services not loaded yet, the view will be applied when they become available
                // We'll use a MutationObserver to detect when services are loaded
                const statusContainer = document.getElementById('status');
                if (statusContainer) {
                    const observer = new MutationObserver((mutations) => {
                        mutations.forEach((mutation) => {
                            if (mutation.type === 'childList') {
                                const servicesGrid = document.querySelector('.services-grid');
                                if (servicesGrid) {
                                    tryApplyViewType();
                                    observer.disconnect(); // Stop observing once applied
                                }
                            }
                        });
                    });
                    
                    observer.observe(statusContainer, {
                        childList: true,
                        subtree: true
                    });
                }
            }
        });

        toggleBtn.addEventListener('click', async () => {
            const newViewType = isListView ? 'card' : 'list';
            
            // Apply the view change immediately
            if (applyViewType(newViewType)) {
                // Save the preference to server
                await saveViewType(newViewType);
            }
        });

        function buildTableFromCards() {
            const table = document.createElement('table');
            table.id = 'services-list-table';
            table.className = 'services-table';

            // inject simple styles once
            if (!document.getElementById('services-table-styles')) {
                const style = document.createElement('style');
                style.id = 'services-table-styles';
                style.textContent = `
                    .services-table {
                        width: 100%;
                        border-collapse: collapse;
                        margin-top: 12px;
                        font-size: 14px;
                    }
                    .services-table th, .services-table td {
                        padding: 8px 10px;
                        border-bottom: 1px solid var(--border, #555);
                        text-align: left;
                    }
                    .services-table thead {
                        background: var(--surface);
                        color: var(--text);
                    }
                    .services-table thead th {
                        font-weight: 600;
                        border-bottom: 2px solid var(--border);
                    }
                    /* Hover row */
                    .services-table tbody tr:hover {
                        background: rgba(0,0,0,0.02);
                        cursor: pointer;
                    }
                    /* Highlight selected rows when in multiselect mode */
                    .services-table tbody tr.selected {
                        background: rgba(0, 123, 255, 0.15);
                        border-left: 4px solid var(--primary, #007bff);
                    }
                    .services-table .status-badge-table {
                        padding: 2px 8px;
                        border-radius: 4px;
                        font-weight: 600;
                        color: var(--surface);
                    }
                    .services-table .badge-ok { background: var(--success); }
                    .services-table .badge-warning { background: var(--warning); }
                    .services-table .badge-critical { background: var(--critical); }
                    .services-table .badge-unknown { background: var(--unknown); }
                    .services-table .badge-pending { background: var(--pending); }
                    .icons-cell i { color: var(--text-secondary); font-size: 14px; }
                `;
                document.head.appendChild(style);
            }

            // Table header
            const thead = document.createElement('thead');
            thead.innerHTML = '<tr><th>Service</th><th style="width:80px;"></th><th>Status</th><th>Last Check</th><th>Duration</th><th>Attempt</th><th>Info</th></tr>';
            table.appendChild(thead);

            const tbody = document.createElement('tbody');

            const hostname = new URLSearchParams(window.location.search).get('ip');

            // Iterate over existing service cards to populate the table
            const cardElements = document.querySelectorAll('.service-card');
            cardElements.forEach(card => {
                const serviceNameEl = card.querySelector('.service-title');
                const statusDetailEl = card.querySelector('.service-details');
                if (!serviceNameEl || !statusDetailEl) return;

                const serviceName = serviceNameEl.textContent;
                const encodedServiceName = serviceName.replace(/\s+/g, '+');

                // Prepare basic values we already have
                const statusTextMatch = statusDetailEl.textContent.match(/Status:\s*(.*)/i);
                const statusText = statusTextMatch ? statusTextMatch[1].trim() : '';
                const statusClass = ['ok', 'warning', 'critical', 'unknown', 'pending']
                    .find(cls => card.classList.contains(cls)) || '';

                // Build row with placeholders for async fields
                const row = document.createElement('tr');
                row.className = `service-row-${statusClass}`;
                row.dataset.service = encodedServiceName;
                row.dataset.serviceName = serviceName;

                // If this service is already in the selectedServices list, visually mark it
                if (typeof selectedServices !== 'undefined' && Array.isArray(selectedServices) && selectedServices.includes(serviceName)) {
                    row.classList.add('selected');
                }

                row.innerHTML = `
                    <td class="svc-name-cell">${serviceName}</td>
                    <td class="icons-cell">Loading...</td>
                    <td class="status-cell">Loading...</td>
                    <td class="last-check-cell">Loading...</td>
                    <td class="duration-cell">–</td>
                    <td class="attempt-cell">–</td>
                    <td class="info-cell">Loading...</td>
                `;

                // Same click / context behaviour
                row.addEventListener('click', () => {
                    card.dispatchEvent(new MouseEvent('click', { bubbles: true }));
                });
                row.addEventListener('contextmenu', e => {
                    e.preventDefault();
                    card.dispatchEvent(new MouseEvent('contextmenu', {
                        bubbles: true,
                        clientX: e.clientX,
                        clientY: e.clientY
                    }));
                });

                tbody.appendChild(row);

                // Fetch extra details for the service from the global variable
                if (window.apmFullStatus && window.apmFullStatus.servicestatus) {
                    const serviceData = window.apmFullStatus.servicestatus.find(s => s.service_description === serviceName);
                    if (serviceData) {
                        const svc = mapServiceForModal(serviceData); // Use helper to get consistent data format
                        
                        // Check if service is pending (status = 1 means pending)
                        const isPending = svc.status === 1;
                        
                        // Handle pending services and invalid timestamps - show N/A for date and duration
                        const lastCheckDisplay = (isPending || !svc.last_check || svc.last_check === 0 || isNaN(svc.last_check)) ? 'N/A' : new Date(svc.last_check).toLocaleString();
                        const durationDisplay = (isPending || !svc.last_state_change || svc.last_state_change === 0 || isNaN(svc.last_state_change)) ? 'N/A' : (typeof calculateDuration === 'function' ? calculateDuration(svc.last_state_change) : '–');
                        
                        // Update cells
                        row.querySelector('.last-check-cell').textContent = lastCheckDisplay;
                        row.querySelector('.duration-cell').textContent = durationDisplay;
                        row.querySelector('.attempt-cell').textContent = `${svc.current_attempt} / ${svc.max_attempts}`;
                        row.querySelector('.info-cell').textContent = svc.plugin_output || '–';

                        // Clone icons from card (service-icon elements and graph icon)
                        let iconsHTML = '';
                        const iconEls = card.querySelectorAll('.service-icon-container i, .service-graphs-available');
                        iconEls.forEach(ic => {
                            const clone = ic.cloneNode(true);
                            // adjust class to fit table styling
                            clone.classList.remove('service-icon');
                            clone.style.marginRight = '4px';
                            iconsHTML += clone.outerHTML;
                        });

                        // Check for graph availability if not already present in card
                        const hasGraphIcon = card.querySelector('.service-graphs-available');
                        if (!hasGraphIcon) {
                            // Check if service has action_url for graphs
                            const serviceObjectUrl = `https://${window.location.hostname}/nagios/cgi-bin/objectjson.cgi?query=service&hostname=${hostname}&servicedescription=${encodedServiceName}`;
                            fetch(serviceObjectUrl, { credentials: 'include' })
                                .then(response => response.ok ? response.json() : Promise.reject('Failed to fetch service object info'))
                                .then(serviceObjectData => {
                                    if (serviceObjectData.result.type_text === 'Success' && 
                                        serviceObjectData.data.service && 
                                        serviceObjectData.data.service.action_url) {
                                        // Add graph icon to both card and table row
                                        addGraphIconToService(card, serviceName);
                                        
                                        // Update the table row with the new graph icon
                                        const tableRow = document.querySelector(`tr[data-service="${encodedServiceName}"]`);
                                        if (tableRow) {
                                            const iconsCell = tableRow.querySelector('.icons-cell');
                                            if (iconsCell) {
                                                const graphIcon = document.createElement('i');
                                                graphIcon.className = 'fa fa-area-chart';
                                                graphIcon.style.marginRight = '4px';
                                                graphIcon.style.color = 'var(--text-secondary)';
                                                graphIcon.title = 'Performance graphs available';
                                                iconsCell.appendChild(graphIcon);
                                            }
                                        }
                                    }
                                })
                                .catch(error => console.log(`Error checking service action URL: ${error}`));
                        }

                        // Build status badge
                        const badgeHTML = `<span class="status-badge-table badge-${statusClass}">${statusText}</span>`;

                        row.innerHTML = `
                            <td class="svc-name-cell">${serviceName}</td>
                            <td class="icons-cell">${iconsHTML}</td>
                            <td class="status-cell">${badgeHTML}</td>
                            <td class="last-check-cell">${lastCheckDisplay}</td>
                            <td class="duration-cell">${durationDisplay}</td>
                            <td class="attempt-cell">${svc.current_attempt} / ${svc.max_attempts}</td>
                            <td class="info-cell">${svc.plugin_output || '–'}</td>
                        `;
                    } else {
                        row.querySelector('.last-check-cell').textContent = 'N/A';
                        row.querySelector('.info-cell').textContent = 'N/A';
                    }
                } else {
                    row.querySelector('.last-check-cell').textContent = 'N/A';
                    row.querySelector('.info-cell').textContent = 'Data not available';
                }
            });

            table.appendChild(tbody);
            return table;
        }
    });
})(); 