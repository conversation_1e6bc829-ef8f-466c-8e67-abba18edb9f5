function showAddForm(type) {
    const formContainer = document.getElementById(type + 'FormContainer');
    const form = document.getElementById(type + 'Form');
    const submitButton = form.querySelector('.save-button');
    const formSection = form.closest('.form-section');
    
    // Close any other open forms first
    document.querySelectorAll('.form-container.visible').forEach(container => {
        if (container !== formContainer) {
            const otherType = container.id.replace('FormContainer', '');
            resetForm(otherType);
        }
    });
    
    // Reset the form but don't call resetForm which would hide it
    form.reset();
    const idInput = form.querySelector('[name="id"]');
    if (idInput) idInput.value = '';
    
    formContainer.classList.add('visible');
    submitButton.innerHTML = '<i class="fa fa-floppy-o"></i>';
    
    // Scroll to the form container
    setTimeout(() => {
        formContainer.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }, 100);
}

function editEntry(type, data) {
    const formContainer = document.getElementById(type + 'FormContainer');
    const form = document.getElementById(type + 'Form');
    const formSection = form.closest('.form-section');
    const submitButton = form.querySelector('.save-button');
    
    // Close any other open forms first
    document.querySelectorAll('.form-container.visible').forEach(container => {
        if (container !== formContainer) {
            const otherType = container.id.replace('FormContainer', '');
            resetForm(otherType);
        }
    });
    
    // Remove existing highlight only from this section's table
    const table = formSection.querySelector('table');
    if (table) {
        table.querySelectorAll('tr.editing-row').forEach(row => {
            row.classList.remove('editing-row');
        });
    }

    // Populate form fields
    for (const [key, value] of Object.entries(data)) {
        const input = form.querySelector(`[name="${key}"]`);
        if (input) {
            input.value = value || '';
        }
    }
    const idInput = form.querySelector('[name="id"]');
    if (idInput) idInput.value = data.id;

    // Highlight the corresponding table row
    if (table) {
        const tr = table.querySelector(`tr[data-row-id="${data.id}"]`);
        if (tr) {
            tr.classList.add('editing-row');
            // Scroll to the row being edited
            setTimeout(() => {
                tr.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }, 100);
        }
    }

    // Show form and update UI
    formContainer.classList.add('visible');
    submitButton.innerHTML = '<i class="fa fa-floppy-o"></i>';
    
    const resetButton = form.querySelector('.reset-btn');
    if (resetButton) resetButton.innerHTML = '<i class="fa fa-times"></i>';
    
    formSection.classList.add('editing');
    
    // Scroll to the form
    setTimeout(() => {
        formContainer.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }, 100);
}

function resetForm(type) {
    const formContainer = document.getElementById(type + 'FormContainer');
    const form = document.getElementById(type + 'Form');
    const formSection = form.closest('.form-section');
    const submitButton = form.querySelector('.save-button');

    form.reset();
    
    // Add a class for the exit animation
    formContainer.classList.add('exiting');
    
    // Wait for animation to complete before removing visibility
    setTimeout(() => {
        formContainer.classList.remove('visible');
        formContainer.classList.remove('exiting');
    }, 300);
    
    // Remove existing highlights
    const table = formSection.querySelector('table');
    if (table) {
        table.querySelectorAll('tr.editing-row').forEach(row => {
            row.classList.remove('editing-row');
        });
    }
    form.querySelector('[name="id"]').value = '';
    submitButton.innerHTML = '<i class="fa fa-floppy-o"></i>';
    
    const resetButton = form.querySelector('.reset-btn');
    if (resetButton) resetButton.innerHTML = '<i class="fa fa-times"></i>';
    
    formSection.classList.remove('editing');
}

function deleteEntry(table, id) {
    // Create a custom confirm dialog
    const overlay = document.createElement('div');
    overlay.className = 'confirm-overlay';
    overlay.style.position = 'fixed';
    overlay.style.top = '0';
    overlay.style.left = '0';
    overlay.style.width = '100%';
    overlay.style.height = '100%';
    overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
    overlay.style.zIndex = '1000';
    overlay.style.display = 'flex';
    overlay.style.justifyContent = 'center';
    overlay.style.alignItems = 'center';
    
    const dialog = document.createElement('div');
    dialog.className = 'confirm-dialog';
    dialog.style.backgroundColor = '#333';
    dialog.style.padding = '20px';
    dialog.style.borderRadius = '8px';
    dialog.style.maxWidth = '400px';
    dialog.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.3)';
    dialog.style.animation = 'fadeIn 0.3s ease';
    
    dialog.innerHTML = `
        <h3 style="color: #cde06b; margin-bottom: 15px;">Confirm Deletion</h3>
        <p style="margin-bottom: 20px; color: #fff;">Are you sure you want to delete this entry? This action cannot be undone.</p>
        <div style="display: flex; justify-content: flex-end; gap: 10px;">
            <button id="cancelDelete" style="padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; background-color: #666; color: white;">Cancel</button>
            <button id="confirmDelete" style="padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; background-color: #d41d28; color: white;">Delete</button>
        </div>
    `;
    
    overlay.appendChild(dialog);
    document.body.appendChild(overlay);
    
    // Handle dialog buttons
    document.getElementById('cancelDelete').addEventListener('click', function() {
        overlay.style.opacity = '0';
        setTimeout(() => {
            document.body.removeChild(overlay);
        }, 300);
    });
    
    document.getElementById('confirmDelete').addEventListener('click', function() {
        window.location.href = `?action=delete&table=${table}&id=${id}&status=success`;
    });
}

function moveUp(table, id) {
    window.location.href = `?action=move&table=${table}&id=${id}&direction=up&status=success`;
}

function moveDown(table, id) {
    window.location.href = `?action=move&table=${table}&id=${id}&direction=down&status=success`;
}

document.addEventListener('DOMContentLoaded', function() {
    // Password toggle logic
    document.querySelectorAll('.toggle-password').forEach(toggle => {
        toggle.addEventListener('click', function() {
            const targetField = this.getAttribute('data-target');
            const td = this.closest('td');
            const hidden = td.querySelector('.password-hidden');
            const text = td.querySelector(`.password-text[data-field="${targetField}"]`);

            if (text.classList.contains('hidden')) {
                text.classList.remove('hidden');
                text.classList.add('visible');
                hidden.style.display = 'none';
                this.classList.remove('fa-eye');
                this.classList.add('fa-eye-slash');
                
                // Auto-hide after 5 seconds
                setTimeout(() => {
                    if (text.classList.contains('visible')) {
                        text.classList.remove('visible');
                        text.classList.add('hidden');
                        hidden.style.display = 'inline';
                        this.classList.remove('fa-eye-slash');
                        this.classList.add('fa-eye');
                    }
                }, 5000);
            } else {
                text.classList.remove('visible');
                text.classList.add('hidden');
                hidden.style.display = 'inline';
                this.classList.remove('fa-eye-slash');
                this.classList.add('fa-eye');
            }
        });
    });

    // Password toggle logic for forms
    document.querySelectorAll('.toggle-password-form').forEach(toggle => {
        toggle.addEventListener('click', function() {
            const container = this.closest('.password-input-container');
            const passwordInput = container.querySelector('input[type="password"], input[type="text"]');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                this.classList.remove('fa-eye');
                this.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                this.classList.remove('fa-eye-slash');
                this.classList.add('fa-eye');
            }
        });
    });

    // Add reset button functionality
    document.querySelectorAll('.form-section').forEach(section => {
        const form = section.querySelector('form');
        if (form) {
            const formId = form.getAttribute('id');
            const type = formId.replace('Form', '');
            
            // Check if reset button already exists
            let resetBtn = form.querySelector('.reset-btn');
            if (!resetBtn) {
                resetBtn = document.createElement('button');
                resetBtn.innerHTML = '<i class="fa fa-times"></i>';
                resetBtn.className = 'action-btn reset-btn';
                resetBtn.type = 'button';
                resetBtn.onclick = () => resetForm(type);
                resetBtn.title = "Cancel";
                
                // Add to action-buttons container if it exists
                const actionBtns = form.querySelector('.action-buttons');
                if (actionBtns) {
                    actionBtns.appendChild(resetBtn);
                } else {
                    const formContainer = section.querySelector('.form-container');
                    if (formContainer) {
                        formContainer.appendChild(resetBtn);
                    }
                }
            }
        }
    });
    
    // Add form submission enhancement
    document.querySelectorAll('form').forEach(form => {
        form.addEventListener('submit', function(e) {
            const submitBtn = form.querySelector('.save-button');
            if (submitBtn) {
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fa fa-spinner fa-spin"></i>';
                submitBtn.disabled = true;
                
                // Re-enable after a delay if the form doesn't submit for some reason
                setTimeout(() => {
                    if (submitBtn.disabled) {
                        submitBtn.disabled = false;
                        submitBtn.innerHTML = originalText;
                    }
                }, 3000);
            }
        });
    });
});

function toggleOtherCredsIframe() {
    const iframeContainer = document.getElementById('otherIframeContainer');
    const iframe = document.getElementById('otherCredsIframe');
    const btn = document.getElementById('other-creds-btn');

    if (iframe.style.display === 'none' || iframe.style.display === '') {
        iframe.src = `https://${window.location.hostname}/ndd/credentialsother.php`;
        iframe.style.display = 'block';
        iframeContainer.classList.add('visible');
        btn.innerHTML = '<i class="fa fa-times"></i> Cancel';
        btn.title = 'Cancel';
        btn.style.background = '#444';

        // Wait for iframe to load before manipulating its content
        iframe.onload = function() {
            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
            
            // Hide nav elements
            const navElements = iframeDoc.getElementsByTagName('nav');
            for (let nav of navElements) {
                nav.style.display = 'none';
            }
            
            // Hide elements with specified classes
            const classesToHide = ['list-inline', 'nav', 'nav-pills'];
            classesToHide.forEach(className => {
                const elements = iframeDoc.getElementsByClassName(className);
                for (let element of elements) {
                    element.style.display = 'none';
                }
            });
            
            // Find the original Cancel button and modify it to List
            const buttons = iframeDoc.getElementsByTagName('a');
            for (let button of buttons) {
                if (button.textContent.trim() === 'Cancel' && button.classList.contains('btn')) {
                    button.innerHTML = '<i class="fa fa-list"></i> List';
                    button.title = 'List';
                    
                    // Create new Cancel button
                    const newCancelButton = iframeDoc.createElement('a');
                    newCancelButton.innerHTML = '<i class="fa fa-times"></i> Cancel';
                    newCancelButton.title = 'Cancel';
                    newCancelButton.className = 'btn btn-default';
                    newCancelButton.role = 'button';
                    newCancelButton.style.marginLeft = '10px';
                    newCancelButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        iframe.src = `https://${window.location.hostname}/ndd/credentialsother.php`;
                    });
                    
                    // Insert new Cancel button after the List button
                    button.parentNode.insertBefore(newCancelButton, button.nextSibling);
                    break;
                }
            }
            
            // Scroll the iframe container into view
            iframeContainer.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        };
    } else {
        btn.innerHTML = '<i class="fa fa-plus"></i> Add';
        btn.title = 'Add Other';
        btn.style.backgroundColor = '#444';
        
        // Add a class for the exit animation
        iframeContainer.classList.add('exiting');
        
        // Wait for animation to complete before removing visibility
        setTimeout(() => {
            iframeContainer.classList.remove('visible');
            iframeContainer.classList.remove('exiting');
            iframe.src = '';
            iframe.style.display = 'none';
        }, 300);
    }
}

function editAzureEntry(data) {
    const formContainer = document.getElementById('azureFormContainer');
    const form = document.getElementById('azureForm');
    const formSection = form.closest('.form-section');
    const submitButton = form.querySelector('.save-button');
    
    // Close any other open forms first
    document.querySelectorAll('.form-container.visible').forEach(container => {
        if (container !== formContainer) {
            const otherType = container.id.replace('FormContainer', '');
            resetForm(otherType);
        }
    });
    
    // Remove existing highlight only from this section's table
    const table = formSection.querySelector('table');
    if (table) {
        table.querySelectorAll('tr.editing-row').forEach(row => {
            row.classList.remove('editing-row');
        });
    }

    // Populate form fields with Azure data
    form.querySelector('[name="tenant_id"]').value = data.TENANT_ID || '';
    form.querySelector('[name="client_id"]').value = data.CLIENT_ID || '';
    form.querySelector('[name="client_secret"]').value = data.CLIENT_SECRET || '';
    form.querySelector('[name="subscription_id"]').value = data.SUBSCRIPTION_ID || '';

    // Highlight the corresponding table row
    if (table) {
        const tr = table.querySelector('tr[data-row-id="azure"]');
        if (tr) {
            tr.classList.add('editing-row');
            // Scroll to the row being edited
            setTimeout(() => {
                tr.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }, 100);
        }
    }

    // Show form and update UI
    formContainer.classList.add('visible');
    submitButton.innerHTML = '<i class="fa fa-floppy-o"></i>';
    
    const resetButton = form.querySelector('.reset-btn');
    if (resetButton) resetButton.innerHTML = '<i class="fa fa-times"></i>';
    
    formSection.classList.add('editing');
    
    // Scroll to the form
    setTimeout(() => {
        formContainer.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }, 100);
}

function deleteAzureEntry() {
    // Create a custom confirm dialog
    const overlay = document.createElement('div');
    overlay.className = 'confirm-overlay';
    overlay.style.position = 'fixed';
    overlay.style.top = '0';
    overlay.style.left = '0';
    overlay.style.width = '100%';
    overlay.style.height = '100%';
    overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
    overlay.style.zIndex = '1000';
    overlay.style.display = 'flex';
    overlay.style.justifyContent = 'center';
    overlay.style.alignItems = 'center';
    
    const dialog = document.createElement('div');
    dialog.className = 'confirm-dialog';
    dialog.style.backgroundColor = '#333';
    dialog.style.padding = '20px';
    dialog.style.borderRadius = '8px';
    dialog.style.maxWidth = '400px';
    dialog.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.3)';
    dialog.style.animation = 'fadeIn 0.3s ease';
    
    dialog.innerHTML = `
        <h3 style="color: #cde06b; margin-bottom: 15px;">Confirm Deletion</h3>
        <p style="margin-bottom: 20px; color: #fff;">Are you sure you want to delete Azure credentials? This action cannot be undone.</p>
        <div style="display: flex; justify-content: flex-end; gap: 10px;">
            <button id="cancelDelete" style="padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; background-color: #666; color: white;">Cancel</button>
            <button id="confirmDelete" style="padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; background-color: #d41d28; color: white;">Delete</button>
        </div>
    `;
    
    overlay.appendChild(dialog);
    document.body.appendChild(overlay);
    
    // Handle dialog buttons
    document.getElementById('cancelDelete').addEventListener('click', function() {
        overlay.style.opacity = '0';
        setTimeout(() => {
            document.body.removeChild(overlay);
        }, 300);
    });
    
    document.getElementById('confirmDelete').addEventListener('click', function() {
        window.location.href = `?action=delete&table=azure&status=success`;
    });
}