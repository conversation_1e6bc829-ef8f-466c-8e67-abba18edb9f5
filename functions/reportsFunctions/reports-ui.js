(function() {
    // UI Elements
    let startInput, endInput, typeSel, hostgroupSel, genBtn, saveBtn, scheduleBtn, viewSavedBtn;
    let contentDiv, statusFilterContainer, srDisableBtn, srStatus;

    // Status options & state
    const hostStatusOptions = [
        { key: 'up', label: 'Up', class: 'ok' },
        { key: 'down', label: 'Down', class: 'critical' },
        { key: 'unreachable', label: 'Unreach', class: 'unknown' }
    ];

    const serviceStatusOptions = [
        { key: 'ok', label: 'OK', class: 'ok' },
        { key: 'warning', label: 'Warn', class: 'warning' },
        { key: 'critical', label: 'Crit', class: 'critical' },
        { key: 'unknown', label: 'Unk', class: 'unknown' }
    ];

    let activeStatuses = new Set();

    // Initialize UI elements
    function initializeElements() {
        startInput = document.getElementById('report-start');
        endInput = document.getElementById('report-end');
        typeSel = document.getElementById('report-type');
        hostgroupSel = document.getElementById('report-hostgroup');
        genBtn = document.getElementById('report-generate');
        saveBtn = document.getElementById('report-save');
        scheduleBtn = document.getElementById('report-schedule');
        viewSavedBtn = document.getElementById('view-saved-reports');
        contentDiv = document.getElementById('reports-content');
        statusFilterContainer = document.getElementById('reports-status-filters');
        srDisableBtn = document.getElementById('sr-disable');
        srStatus = document.getElementById('sr-status');
    }

    // Initialize date pickers using server time if possible
    function initDates() {
        const progUrl = '/nagios/cgi-bin/statusjson.cgi?query=programstatus';
        fetch(progUrl, { credentials: 'include' })
            .then(r => r.json())
            .then(js => js?.data?.programstatus?.current_time)
            .then(serverSec => {
                const endDate = serverSec ? new Date(serverSec * 1000) : new Date();
                endInput.value = window.reportsCore.toLocalIso(endDate);
                startInput.value = window.reportsCore.toLocalIso(new Date(endDate.getTime() - 24 * 60 * 60 * 1000));
            })
            .catch(() => {
                const now = new Date();
                endInput.value = window.reportsCore.toLocalIso(now);
                startInput.value = window.reportsCore.toLocalIso(new Date(now.getTime() - 24 * 60 * 60 * 1000));
            });
    }

    // ------------------ Dropdown population helpers ------------------

    // Remove all options except the first ("All")
    function resetSelectOptions(sel) {
        while (sel.options.length > 1) sel.remove(1);
    }

    // Generic helper to append unique option values keeping "All" first
    function appendOptions(arr) {
        const existing = new Set(Array.from(hostgroupSel.options).map(o => o.value));
        arr.sort().forEach(v => {
            if (!existing.has(v)) {
                const opt = document.createElement('option');
                opt.value = v;
                opt.textContent = v;
                hostgroupSel.appendChild(opt);
            }
        });
    }

    // Populate hostgroups dropdown using availability endpoint only
    function populateHostgroups() {
        resetSelectOptions(hostgroupSel);
        const nowSec = Math.floor(Date.now() / 1000);
        const url = `/nagios/cgi-bin/archivejson.cgi?query=availability&availabilityobjecttype=hostgroups&starttime=${nowSec - 3600}&endtime=${nowSec}`;
        fetch(url, { credentials: 'include' })
            .then(r => (r.ok ? r.json() : Promise.reject(r.status)))
            .then(js => {
                const arr = js?.data?.hostgroups?.map(g => g.name) || (js?.data?.hostgroup ? [js.data.hostgroup.name] : []);
                if (arr.length) {
                    appendOptions(arr);
                } else if (typeof fetchAllHostGroups === 'function') {
                    // ultimate fallback DB
                    fetchAllHostGroups().then(appendOptions);
                }
            })
            .catch(() => {
                if (typeof fetchAllHostGroups === 'function') fetchAllHostGroups().then(appendOptions);
            });
    }

    // Populate hosts dropdown using availability endpoint
    function populateHosts() {
        resetSelectOptions(hostgroupSel);
        const nowSec = Math.floor(Date.now() / 1000);
        const url = `/nagios/cgi-bin/archivejson.cgi?query=availability&availabilityobjecttype=hosts&starttime=${nowSec - 3600}&endtime=${nowSec}`;
        fetch(url, { credentials: 'include' })
            .then(r => (r.ok ? r.json() : Promise.reject(r.status)))
            .then(js => {
                const arr = js?.data?.hosts?.map(h => h.name) || (js?.data?.host ? [js.data.host.name] : []);
                if (arr.length) {
                    appendOptions(arr);
                } else if (typeof fetchAllHosts === 'function') {
                    // If there's a helper available for full host list
                    fetchAllHosts().then(appendOptions);
                }
            })
            .catch(() => {
                if (typeof fetchAllHosts === 'function') fetchAllHosts().then(appendOptions);
            });
    }

    // Update dropdown + label based on selected report type
    function refreshEntityDropdown() {
        const lbl = document.querySelector('label[for="report-hostgroup"]');
        if (typeSel.value === 'services') {
            lbl.textContent = 'Host';
            populateHosts();
        } else {
            lbl.textContent = 'Hostgroup';
            populateHostgroups();
        }
    }

    // Build status filters
    function buildStatusFilters(type){
        statusFilterContainer.innerHTML = '';
        const opts = type === 'services' ? serviceStatusOptions : hostStatusOptions;
        activeStatuses = new Set(opts.map(o=>o.key));
        opts.forEach(o=>{
            const btn = document.createElement('button');
            btn.className = `reports-status-filter ${o.class} active`;
            btn.dataset.statusKey = o.key;
            btn.title = o.label;
            btn.textContent = o.label;
            btn.addEventListener('click', ()=>{
                if (btn.classList.contains('active')){
                    btn.classList.remove('active');
                    activeStatuses.delete(o.key);
                } else {
                    btn.classList.add('active');
                    activeStatuses.add(o.key);
                }
                window.reportsFilters.applyStatusFilter();
            });
            statusFilterContainer.appendChild(btn);
        });
    }

    // UI State Management
    function showLoading() {
        contentDiv.innerHTML = '<div class="reports-loading"><i class="fa fa-spinner fa-spin"></i> Loading...</div>';
    }

    function showError(message) {
        contentDiv.innerHTML = `<div class="reports-error">${message}</div>`;
    }

    function showEmpty(message) {
        contentDiv.innerHTML = `<div class="reports-empty">${message}</div>`;
    }

    // Getter methods for external access
    function getSelectedType() {
        return typeSel.value;
    }

    function getStartInput() {
        return startInput;
    }

    function getEndInput() {
        return endInput;
    }

    function getHostgroupValue() {
        return hostgroupSel.value;
    }

    function getActiveStatuses() {
        return activeStatuses;
    }

    // Event Handlers
    function setupEventListeners() {
        // Attach handlers
        genBtn.addEventListener('click', window.reportsCore.generateReport);
        if(saveBtn) saveBtn.addEventListener('click', window.reportsSave.saveReport);
        if(viewSavedBtn) viewSavedBtn.addEventListener('click', window.reportsSave.openSavedReportsModal);
        
        // Schedule button handler
        if(scheduleBtn){
            scheduleBtn.addEventListener('click', ()=>{
                const scheduleModal = document.getElementById('scheduleReportModal');
                if(!scheduleModal) return;

                // Show modal first (UX) then attempt to load config
                scheduleModal.style.display = 'flex';

                // Reset form to defaults & status message
                document.getElementById('sr-email').value = '';
                document.getElementById('sr-frequency').value = 'daily';
                document.getElementById('sr-time').value = '00:05';
                document.getElementById('sr-range').value = 1;
                // Reset status checkboxes (select all by default)
                document.querySelectorAll('#sr-host-statuses input').forEach(cb => cb.checked = true);
                document.querySelectorAll('#sr-svc-statuses input').forEach(cb => cb.checked = true);
                if(srDisableBtn) srDisableBtn.style.display = 'none';
                if(srStatus){
                    srStatus.textContent = 'Checking current schedule…';
                    srStatus.className = 'sr-status';
                }

                // Fetch current cron config
                fetch('functions/reportsFunctions/getReportCron.php')
                    .then(r=>r.json())
                    .then(js=>{
                        if(!js.success) return;
                        if(js.enabled && js.config){
                            const cfg = js.config;
                            if(cfg.email) document.getElementById('sr-email').value = cfg.email;
                            if(cfg.frequency) document.getElementById('sr-frequency').value = cfg.frequency;
                            if(cfg.time) document.getElementById('sr-time').value = cfg.time;
                            if(cfg.range) document.getElementById('sr-range').value = cfg.range;
                            // Apply stored status filters
                            if(cfg.hostStatuses){
                                const arr = cfg.hostStatuses.split(',');
                                document.querySelectorAll('#sr-host-statuses input').forEach(cb=>{cb.checked = arr.includes(cb.value);});
                            }
                            if(cfg.svcStatuses){
                                const arrS = cfg.svcStatuses.split(',');
                                document.querySelectorAll('#sr-svc-statuses input').forEach(cb=>{cb.checked = arrS.includes(cb.value);});
                            }
                            if(srDisableBtn) srDisableBtn.style.display = 'inline-block';
                            if(srStatus){
                                srStatus.textContent = 'Report is currently scheduled';
                                srStatus.classList.add('scheduled');
                            }
                        } else {
                            if(srStatus){
                                srStatus.textContent = 'No report scheduled';
                                srStatus.classList.add('not-scheduled');
                            }
                        }
                    })
                    .catch(err=>{
                        console.warn('Failed to fetch cron config', err);
                        if(srStatus){
                            srStatus.textContent = 'Unable to retrieve schedule status';
                        }
                    });
            });
        }

        // update filters when type changes
        typeSel.addEventListener('change', () => {
            buildStatusFilters(typeSel.value);
            refreshEntityDropdown();
        });

        // Setup modal handlers
        setupModalHandlers();
    }

    function setupModalHandlers() {
        const scheduleModal = document.getElementById('scheduleReportModal');
        const savedReportsModal = document.getElementById('savedReportsModal');
        const saveReportNameModal = document.getElementById('saveReportNameModal');
        const srClose = document.querySelectorAll('.sr-close');
        const srCancel = document.getElementById('sr-cancel');
        const srForm   = document.getElementById('schedule-form');
        const srSendNow = document.getElementById('sr-send-now');
        const saveReportForm = document.getElementById('save-report-form');
        const saveReportCancel = document.getElementById('save-report-cancel');

        // Close modal handlers
        srClose.forEach(closeBtn => {
            closeBtn.addEventListener('click', () => {
                const modal = closeBtn.closest('.sr-modal');
                if (modal) modal.style.display = 'none';
            });
        });
        
        // Click outside modal to close
        document.addEventListener('click', (event) => {
            const modals = document.querySelectorAll('.sr-modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        });
        
        if(srCancel){srCancel.addEventListener('click', ()=>{scheduleModal.style.display='none';});}
        
        // Save report name modal handlers
        if(saveReportCancel){
            saveReportCancel.addEventListener('click', ()=>{
                if(saveReportNameModal) saveReportNameModal.style.display='none';
            });
        }
        
        if(saveReportForm){
            saveReportForm.addEventListener('submit', function(e){
                e.preventDefault();
                const reportName = document.getElementById('report-name').value.trim();
                window.reportsSave.handleSaveReportForm(reportName);
            });
        }
        
        // Send Now button handler
        if(srSendNow){
            srSendNow.addEventListener('click', function(){
                const email = document.getElementById('sr-email').value.trim();
                const range = parseInt(document.getElementById('sr-range').value,10) || 1;

                // Validate multiple emails separated by semicolons
                const emails = email.split(';').map(e => e.trim()).filter(e => e.length > 0);
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                for (const singleEmail of emails) {
                    if (!emailRegex.test(singleEmail)) {
                        alert('Please provide valid email address(es). Invalid email: ' + singleEmail);
                        return;
                    }
                }
                if (emails.length === 0) {
                    alert('Please provide at least one valid email address.');
                    return;
                }

                const params = new URLSearchParams();
                params.append('email', email);
                params.append('range', range);
                // Append chosen status filters
                const hostStatuses = Array.from(document.querySelectorAll('#sr-host-statuses input:checked')).map(cb=>cb.value).join(',');
                const svcStatuses  = Array.from(document.querySelectorAll('#sr-svc-statuses input:checked')).map(cb=>cb.value).join(',');
                params.append('hostStatuses', hostStatuses);
                params.append('svcStatuses', svcStatuses);
                params.append('saveToServer', document.getElementById('sr-save-to-server').checked);

                const sendNowBtn = document.getElementById('sr-send-now');
                sendNowBtn.disabled = true;
                sendNowBtn.textContent = 'Sending...';

                fetch('functions/reportsFunctions/sendInstantReport.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: params.toString()
                }).then(r=>r.json())
                  .then(js=>{
                      if(js.success){
                          if(srStatus){
                              srStatus.textContent = js.message || 'Report sent successfully';
                              srStatus.classList.remove('not-scheduled');
                              srStatus.classList.add('scheduled');
                          }
                          // Keep modal open so user sees status
                      } else {
                          if(srStatus){
                              srStatus.textContent = js.message || 'Failed to send report';
                              srStatus.classList.remove('scheduled');
                              srStatus.classList.add('not-scheduled');
                          }
                      }
                  })
                  .catch(err=>{
                      console.error('Send now error', err);
                      if(srStatus){
                          srStatus.textContent = 'Error sending report';
                          srStatus.classList.remove('scheduled');
                          srStatus.classList.add('not-scheduled');
                      }
                  })
                  .finally(()=>{
                      sendNowBtn.disabled = false;
                      sendNowBtn.textContent = 'Send Now';
                  });
            });
        }
        
        if(srForm){
            srForm.addEventListener('submit', function(e){
                e.preventDefault();
                const email = document.getElementById('sr-email').value.trim();
                const freq  = document.getElementById('sr-frequency').value;
                const time  = document.getElementById('sr-time').value || '00:05';
                const range = parseInt(document.getElementById('sr-range').value,10) || 1;

                // Validate multiple emails separated by semicolons
                const emails = email.split(';').map(e => e.trim()).filter(e => e.length > 0);
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                for (const singleEmail of emails) {
                    if (!emailRegex.test(singleEmail)) {
                        alert('Please provide valid email address(es). Invalid email: ' + singleEmail);
                        return;
                    }
                }
                if (emails.length === 0) {
                    alert('Please provide at least one valid email address.');
                    return;
                }

                const params = new URLSearchParams();
                params.append('email', email);
                params.append('frequency', freq);
                params.append('time', time);
                params.append('range', range);
                // Append chosen status filters
                const hostStatuses = Array.from(document.querySelectorAll('#sr-host-statuses input:checked')).map(cb=>cb.value).join(',');
                const svcStatuses  = Array.from(document.querySelectorAll('#sr-svc-statuses input:checked')).map(cb=>cb.value).join(',');
                params.append('hostStatuses', hostStatuses);
                params.append('svcStatuses', svcStatuses);
                params.append('saveToServer', document.getElementById('sr-save-to-server').checked);

                const saveBtn = document.getElementById('sr-save');
                saveBtn.disabled = true;
                saveBtn.textContent = 'Saving...';

                fetch('functions/reportsFunctions/addReportCron.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: params.toString()
                }).then(r=>r.json())
                  .then(js=>{
                      if(js.success){
                          if(srStatus){
                              srStatus.textContent = 'Report scheduled successfully';
                              srStatus.classList.remove('not-scheduled');
                              srStatus.classList.add('scheduled');
                          }
                          // Keep modal open so user sees status
                      } else {
                          if(srStatus){
                              srStatus.textContent = js.message || 'Failed to schedule report';
                              srStatus.classList.remove('scheduled');
                              srStatus.classList.add('not-scheduled');
                          }
                      }
                  })
                  .catch(err=>{
                      console.error('Schedule error', err);
                      alert('Error scheduling report');
                  })
                  .finally(()=>{
                      saveBtn.disabled = false;
                      saveBtn.textContent = 'Save';
                  });
            });
        }
        
        // Disable scheduled report handler
        if(srDisableBtn){
            srDisableBtn.addEventListener('click', ()=>{
                srDisableBtn.disabled = true;
                srDisableBtn.textContent = 'Disabling...';
                fetch('functions/reportsFunctions/deleteReportCron.php', { method: 'POST' })
                    .then(r=>r.json())
                    .then(js=>{
                        if(js.success){
                            srDisableBtn.style.display = 'none';
                            if(srStatus){
                                srStatus.textContent = 'No report scheduled';
                                srStatus.classList.remove('scheduled');
                                srStatus.classList.add('not-scheduled');
                            }
                        } else {
                            if(srStatus){
                                srStatus.textContent = js.message || 'Failed to disable scheduled report';
                                srStatus.classList.remove('scheduled');
                                srStatus.classList.add('not-scheduled');
                            }
                        }
                    })
                    .catch(err=>{
                        console.error('Disable schedule error', err);
                        if(srStatus){
                            srStatus.textContent = 'Error disabling schedule';
                            srStatus.classList.remove('scheduled');
                            srStatus.classList.add('not-scheduled');
                        }
                    })
                    .finally(()=>{
                        srDisableBtn.disabled = false;
                        srDisableBtn.textContent = 'Disable';
                    });
            });
        }
    }

    // Initialize UI
    function init() {
        initializeElements();
        initDates();
        buildStatusFilters(typeSel.value);
        refreshEntityDropdown();
        setupEventListeners();
    }

    // Export to global scope
    window.reportsUI = {
        init,
        getSelectedType,
        getStartInput,
        getEndInput,
        getHostgroupValue,
        getActiveStatuses,
        showLoading,
        showError,
        showEmpty,
        buildStatusFilters
    };
})(); 