(function() {
    // Helper function to create pie chart
    function createPieChart(containerId, data, type, totalHosts) {
        const container = document.getElementById(containerId);
        if (!container || !data || data.length === 0) return;
        
        // Clear previous content
        container.innerHTML = '';
        
        // Create chart wrapper
        const chartWrapper = document.createElement('div');
        chartWrapper.className = 'pie-chart-wrapper';
        container.appendChild(chartWrapper);
        
        // Create SVG container
        const size = 200;
        const radius = size / 2;
        
        const svg = d3.select(chartWrapper)
            .append('svg')
            .attr('viewBox', `0 0 ${size} ${size}`)
            .attr('preserveAspectRatio', 'xMidYMid meet')
            .style('width', '200px')
            .style('height', '200px');
        
        const g = svg.append('g').attr('transform', `translate(${radius},${radius})`);
        
        // Create pie chart
        const pie = d3.pie().value(d => d.value);
        const arc = d3.arc().innerRadius(radius * 0.3).outerRadius(radius * 0.8);
        
        // Add slices
        g.selectAll('path')
            .data(pie(data))
            .enter()
            .append('path')
            .attr('d', arc)
            .attr('fill', d => d.data.color)
            .attr('stroke', '#fff')
            .attr('stroke-width', 2)
            .append('title')
            .text(d => `${d.data.label}: ${d.data.value} (${((d.data.value / totalHosts) * 100).toFixed(1)}%)`);
        
        // Add legend
        const legendContainer = document.createElement('div');
        legendContainer.className = 'pie-chart-legend';
        container.appendChild(legendContainer);
        
        data.forEach(d => {
            const legendItem = document.createElement('div');
            legendItem.className = 'pie-legend-item';
            
            const colorBox = document.createElement('span');
            colorBox.className = 'pie-legend-color';
            colorBox.style.background = d.color;
            
            const label = document.createElement('span');
            label.className = 'pie-legend-label';
            const percentage = ((d.value / totalHosts) * 100).toFixed(1);
            label.textContent = `${d.label}: ${d.value} (${percentage}%)`;
            
            legendItem.appendChild(colorBox);
            legendItem.appendChild(label);
            legendContainer.appendChild(legendItem);
        });
    }

    // Helper function to create Marimekko chart
    function createMarimekkoChart(containerId, data, type) {
        const container = document.getElementById(containerId);
        if (!container || !data || data.length === 0) {
            container.innerHTML = '<div class="marimekko-empty">No issues to display</div>';
            return;
        }

        // Clear previous content
        container.innerHTML = '';

        // Create chart wrapper
        const chartWrapper = document.createElement('div');
        chartWrapper.className = 'marimekko-chart-wrapper';
        container.appendChild(chartWrapper);

        // Chart dimensions
        const margin = { top: 10, right: 10, bottom: 10, left: 10 };
        const width = 600 - margin.left - margin.right;
        const height = 400 - margin.top - margin.bottom;

        // Create SVG
        const svg = d3.select(chartWrapper)
            .append('svg')
            .attr('viewBox', `0 0 ${width + margin.left + margin.right} ${height + margin.top + margin.bottom}`)
            .attr('preserveAspectRatio', 'xMidYMid meet')
            .style('width', '100%')
            .style('height', 'auto')
            .style('max-width', '600px');

        const g = svg.append('g')
            .attr('transform', `translate(${margin.left},${margin.top})`);

        // Calculate total area
        const totalValue = data.reduce((sum, d) => sum + d.totalIssues, 0);

        // Use D3's treemap layout for proper Marimekko positioning
        const treemap = d3.treemap()
            .size([width, height])
            .padding(2)
            .round(true);

        // Create hierarchy for treemap
        const root = d3.hierarchy({children: data})
            .sum(d => d.totalIssues)
            .sort((a, b) => b.value - a.value);

        treemap(root);

        // Create rectangles for each item
        const leaf = g.selectAll('.leaf')
            .data(root.leaves())
            .enter()
            .append('g')
            .attr('class', 'leaf')
            .attr('transform', d => `translate(${d.x0},${d.y0})`);

        // Calculate section heights within each rectangle
        leaf.each(function(d) {
            const leafGroup = d3.select(this);
            const rectHeight = d.y1 - d.y0;
            const rectWidth = d.x1 - d.x0;
            const item = d.data;

            let currentY = 0;
            item.sections.forEach(section => {
                const sectionHeight = (section.value / item.totalIssues) * rectHeight;

                const rect = leafGroup.append('rect')
                    .attr('x', 0)
                    .attr('y', currentY)
                    .attr('width', rectWidth)
                    .attr('height', sectionHeight)
                    .attr('fill', section.color)
                    .attr('stroke', '#fff')
                    .attr('stroke-width', 1)
                    .style('cursor', 'pointer')
                    .on('click', function() {
                        // Scroll to the corresponding table row
                        const targetElement = document.getElementById(item.scrollId);
                        if (targetElement) {
                            const contentContainer = document.querySelector('.reports-content');
                            const headerHeight = document.querySelector('.reports-controls').offsetHeight;
                            
                            // Use getBoundingClientRect for more accurate positioning
                            const containerRect = contentContainer.getBoundingClientRect();
                            const targetRect = targetElement.getBoundingClientRect();
                            
                            // Calculate the scroll position needed to bring target into view
                            const currentScrollTop = contentContainer.scrollTop;
                            const targetTopRelativeToContainer = targetRect.top - containerRect.top;
                            const scrollOffset = targetTopRelativeToContainer - headerHeight - 20;
                            
                            // Calculate the new scroll position
                            const newScrollTop = currentScrollTop + scrollOffset;
                            
                            // Ensure we don't scroll to negative positions
                            const finalScrollTop = Math.max(0, newScrollTop);
                            
                            // Smooth scroll to the target
                            contentContainer.scrollTo({
                                top: finalScrollTop,
                                behavior: 'smooth'
                            });
                            
                            // Add a brief highlight effect to the target row
                            targetElement.classList.add('highlighted');
                            setTimeout(() => {
                                targetElement.classList.remove('highlighted');
                            }, 2000);
                        } else {
                            // Show a brief message if the target is not found (e.g., filtered out)
                            const contentDiv = document.querySelector('.reports-content');
                            const message = document.createElement('div');
                            message.style.cssText = 'position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(0,0,0,0.8); color: white; padding: 10px 20px; border-radius: 5px; z-index: 1000; font-size: 14px;';
                            message.textContent = `${item.name} not found in current view (may be filtered out)`;
                            contentDiv.appendChild(message);
                            setTimeout(() => {
                                contentDiv.removeChild(message);
                            }, 2000);
                        }
                    })
                    .append('title')
                    .text(`${item.name}\n${section.label}: ${section.value.toFixed(1)}%\nClick to scroll to table row`);

                currentY += sectionHeight;
            });

            // Add text labels if rectangle is large enough
            if (rectWidth > 60 && rectHeight > 30) {
                const fontSize = Math.min(12, rectWidth / 10, rectHeight / 6);

                leafGroup.append('text')
                    .attr('x', rectWidth / 2)
                    .attr('y', rectHeight / 2)
                    .attr('text-anchor', 'middle')
                    .attr('dominant-baseline', 'middle')
                    .attr('font-size', fontSize + 'px')
                    .attr('fill', '#fff')
                    .attr('font-weight', 'bold')
                    .style('pointer-events', 'none') // Prevent text from interfering with click events
                    .text(item.name.length > 15 ? item.name.substring(0, 15) + '...' : item.name);
            }
        });
    }

    // Render functions
    function renderTable(json, type, ipMap = {}) {
        let html = '';
        
        // Generate summary section
        const summary = window.reportsCore.calculateSummary(json, type);
        if (summary) {
            html += '<div class="report-summary">';
            if (type === 'services') {
                html += '<h2 class="summary-title">Summary of All Monitored Services</h2>';
            } else {
                html += '<h2 class="summary-title">Summary of All Monitored Hosts</h2>';
            }
            html += '<div class="charts-container">';
            html += '<div class="chart-section">';
            html += '<h3 class="chart-title">Overall Distribution</h3>';
            html += '<div id="summary-pie-chart" class="summary-pie-chart"></div>';
            html += '</div>';
            html += '<div class="chart-section">';
            html += '<h3 class="chart-title">Issues Breakdown</h3>';
            html += '<div id="summary-marimekko-chart" class="summary-marimekko-chart"></div>';
            html += '</div>';
            html += '</div>';
            html += '</div>';
        }
        
        if (type === 'services') {
            const services = json?.data?.services || [];
            if (!services.length) {
                window.reportsUI.showEmpty('No data for selected parameters');
                return;
            }

            // --- Build host -> services map -----
            const hostMap = services.reduce((map, svc) => {
                if (!map[svc.host_name]) map[svc.host_name] = [];
                map[svc.host_name].push(svc);
                return map;
            }, {});

            // --- Render each host section -----
            Object.keys(hostMap).sort().forEach(host => {
                const ip = ipMap[host];
                const hostDisplay = ip && ip !== host ? `${host} (${ip})` : host;
                
                // Create clickable host link
                const hostLink = `<a href="#" class="host-link" onclick="openHostInModal('${host.replace(/'/g, "\\'")}', '${(ip || host).replace(/'/g, "\\'")}', 'External', false); return false;" title="View host details">${hostDisplay}</a>`;
                
                html += `<h3 class="report-title">${hostLink}</h3>`;
                html += '<table class="report-table service-table"><thead><tr><th>Service</th><th>OK %</th><th>Warn %</th><th>Crit %</th><th>Unk %</th></tr></thead><tbody>';

                hostMap[host].forEach(svc => {
                    const total = (+svc.time_ok || 0) + (+svc.time_warning || 0) + (+svc.time_critical || 0) + (+svc.time_unknown || 0);
                    const pctNumOk = total ? ((+svc.time_ok || 0)/total)*100 : 0;
                    const pctNumWarn = total ? ((+svc.time_warning || 0)/total)*100 : 0;
                    const pctNumCrit = total ? ((+svc.time_critical || 0)/total)*100 : 0;
                    const pctNumUnk = total ? ((+svc.time_unknown || 0)/total)*100 : 0;
                    const fmt = n => n.toFixed(1);
                    // Determine all applicable statuses for filtering
                    const statuses = [];
                    if ((+svc.time_critical||0)>0) statuses.push('critical');
                    if ((+svc.time_warning||0)>0) statuses.push('warning');
                    if ((+svc.time_unknown||0)>0) statuses.push('unknown');
                    if ((+svc.time_ok||0)>0) statuses.push('ok');
                    
                    // Use primary status for display (prioritize critical > warning > unknown > ok)
                    const primaryStatus = (+svc.time_critical||0)>0 ? 'critical' : ((+svc.time_warning||0)>0 ? 'warning' : ((+svc.time_unknown||0)>0 ? 'unknown' : 'ok'));
                    
                    // Create scroll ID for this service
                    const scrollId = `service-${svc.host_name}-${svc.description}`.replace(/[^a-zA-Z0-9-]/g, '-');
                    
                    // Debug: Log service details
                    //console.log('Service:', svc.description, 'Critical:', +svc.time_critical, 'Unknown:', +svc.time_unknown, 'Statuses:', statuses, 'Primary:', primaryStatus);
                    
                    html += `<tr id="${scrollId}" data-status="${primaryStatus}" data-all-statuses="${statuses.join(',')}">
                        <td>${svc.description}</td>
                        <td${pctNumOk>0? ' class="ok"':''}>${fmt(pctNumOk)}</td>
                        <td${pctNumWarn>0? ' class="warning"':''}>${fmt(pctNumWarn)}</td>
                        <td${pctNumCrit>0? ' class="critical"':''}>${fmt(pctNumCrit)}</td>
                        <td${pctNumUnk>0? ' class="unknown"':''}>${fmt(pctNumUnk)}</td>
                    </tr>`;
                });
                html += '</tbody></table>';
            });
        } else if (type === 'hostgroups') {
            const hostgroups = json?.data?.hostgroups || [];
            // Handle both array and single-object responses
            const grpArr = Array.isArray(hostgroups) && hostgroups.length > 0 ? hostgroups : (json?.data?.hostgroup ? [json.data.hostgroup] : []);

            if (!grpArr.length) {
                window.reportsUI.showEmpty('No data for selected parameters');
                return;
            }

            grpArr.forEach(g => {
                const hostList = g.hosts || [];
                if (!hostList.length) return; // Skip empty hostgroups

                html += `<h3 class="report-title">${g.name}</h3>`;
                html += '<table class="report-table hostgroup-table"><thead><tr><th>Host</th><th>Up %</th><th>Down %</th><th>Unreachable %</th></tr></thead><tbody>';
                hostList.forEach(h => {
                    const total = (+h.time_up || 0) + (+h.time_down || 0) + (+h.time_unreachable || 0);
                    const pctNumUp = total ? ((+h.time_up || 0)/total)*100 : 0;
                    const pctNumDown = total ? ((+h.time_down || 0)/total)*100 : 0;
                    const pctNumUnreach = total ? ((+h.time_unreachable || 0)/total)*100 : 0;
                    const fmtH = n=>n.toFixed(1);
                    const status = (+h.time_down||0)>0? 'down' : ((+h.time_unreachable||0)>0 ? 'unreachable':'up');
                    const ip = ipMap[h.name];
                    const hostDisplay = ip && ip !== h.name ? `${h.name} (${ip})` : h.name;
                    
                    // Create clickable host link
                    const hostLink = `<a href="#" class="host-link" onclick="openHostInModal('${h.name.replace(/'/g, "\\'")}', '${(ip || h.name).replace(/'/g, "\\'")}', 'External', false); return false;" title="View host details">${hostDisplay}</a>`;
                    
                    // Create scroll ID for this host
                    const scrollId = `host-${h.name}`.replace(/[^a-zA-Z0-9-]/g, '-');
                    
                    html += `<tr id="${scrollId}" data-status="${status}">
                            <td>${hostLink}</td>
                            <td${pctNumUp>0? ' class="ok"':''}>${fmtH(pctNumUp)}</td>
                            <td${pctNumDown>0? ' class="critical"':''}>${fmtH(pctNumDown)}</td>
                            <td${pctNumUnreach>0? ' class="unknown"':''}>${fmtH(pctNumUnreach)}</td>
                        </tr>`;
                });
                html += '</tbody></table>';
            });
        } else {
            window.reportsUI.showEmpty('Unsupported report type');
            return;
        }
        
        // Set the HTML content first
        document.getElementById('reports-content').innerHTML = html;
        
        // Create pie chart if summary exists
        if (summary) {
            let pieData = [];
            
            if (type === 'services') {
                // Services pie chart data
                const okCount = summary.total - summary.warning - summary.critical - summary.unknown;
                pieData = [
                    { label: 'Services Operating Normally', value: okCount, color: window.reportsCore.getStatusColor('success') },
                    { label: 'Services with Warning State', value: summary.warning, color: window.reportsCore.getStatusColor('warning') },
                    { label: 'Services with Critical State', value: summary.critical, color: window.reportsCore.getStatusColor('critical') },
                    { label: 'Services with Unknown State', value: summary.unknown, color: window.reportsCore.getStatusColor('unknown') }
                ].filter(d => d.value > 0);
            } else {
                // Hosts pie chart data
                const upCount = summary.total - summary.down - summary.unreachable;
                pieData = [
                    { label: 'Hosts Operating Normally', value: upCount, color: window.reportsCore.getStatusColor('success') },
                    { label: 'Hosts with Down State', value: summary.down, color: window.reportsCore.getStatusColor('critical') },
                    { label: 'Hosts with Unreachable State', value: summary.unreachable, color: window.reportsCore.getStatusColor('unknown') }
                ].filter(d => d.value > 0);
            }
            
            // Create the pie chart
            if (pieData.length > 0) {
                createPieChart('summary-pie-chart', pieData, type, summary.total);
            }

            // Prepare Marimekko chart data
            const marimekkoData = window.reportsCore.prepareMarimekkoData(json, type);
            console.log('Marimekko data:', marimekkoData);
            if (marimekkoData.length > 0) {
                createMarimekkoChart('summary-marimekko-chart', marimekkoData, type);
            } else {
                console.log('No Marimekko data available');
            }
        }
    }

    // Export to global scope
    window.reportsRenderer = {
        renderTable,
        createPieChart,
        createMarimekkoChart
    };
})(); 