(function() {
    // Helper: convert seconds to formatted duration (h m s)
    function formatDuration(sec) {
        const h = Math.floor(sec / 3600);
        const m = Math.floor((sec % 3600) / 60);
        const s = sec % 60;
        return `${h}h ${m}m ${s}s`;
    }

    // Helper: local ISO string without seconds for <input type="datetime-local">
    function toLocalIso(dateObj) {
        const tzOffset = dateObj.getTimezoneOffset() * 60000;
        return new Date(dateObj.getTime() - tzOffset).toISOString().slice(0, 16);
    }

    // Helper: fetch hostname -> IP address map via Nagios objectjson API
    async function fetchHostIpMap(){
        try {
            const resp = await fetch('/nagios/cgi-bin/objectjson.cgi?query=hostlist&details=true', { credentials: 'include' });
            if(!resp.ok) throw new Error(resp.statusText);
            const js = await resp.json();
            const map = {};
            const hostlist = js?.data?.hostlist || {};
            Object.entries(hostlist).forEach(([hostname, obj])=>{
                if(obj && obj.address){
                    map[hostname] = obj.address;
                }
            });
            return map;
        } catch(err){
            console.warn('Failed to fetch host IP map', err);
            return {}; // graceful fallback
        }
    }

    // Helper function to calculate summary statistics
    function calculateSummary(data, type) {
        if (type === 'services') {
            const services = data?.data?.services || [];
            if (!services.length) return null;
            
            let totalServices = services.length;
            let servicesWithWarnings = 0;
            let servicesWithCritical = 0;
            let servicesWithUnknown = 0;
            
            services.forEach(svc => {
                if ((+svc.time_warning || 0) > 0) servicesWithWarnings++;
                if ((+svc.time_critical || 0) > 0) servicesWithCritical++;
                if ((+svc.time_unknown || 0) > 0) servicesWithUnknown++;
            });
            
            return {
                total: totalServices,
                warning: servicesWithWarnings,
                critical: servicesWithCritical,
                unknown: servicesWithUnknown,
                warningPct: (servicesWithWarnings / totalServices * 100).toFixed(1),
                criticalPct: (servicesWithCritical / totalServices * 100).toFixed(1),
                unknownPct: (servicesWithUnknown / totalServices * 100).toFixed(1)
            };
        } else {
            const hostgroups = data?.data?.hostgroups || [];
            const grpArr = Array.isArray(hostgroups) && hostgroups.length > 0 ? hostgroups : (data?.data?.hostgroup ? [data.data.hostgroup] : []);
            
            if (!grpArr.length) return null;
            
            let totalHosts = 0;
            let hostsWithDown = 0;
            let hostsWithUnreachable = 0;
            
            grpArr.forEach(g => {
                const hostList = g.hosts || [];
                totalHosts += hostList.length;
                hostList.forEach(h => {
                    if ((+h.time_down || 0) > 0) hostsWithDown++;
                    if ((+h.time_unreachable || 0) > 0) hostsWithUnreachable++;
                });
            });
            
            return {
                total: totalHosts,
                down: hostsWithDown,
                unreachable: hostsWithUnreachable,
                downPct: (hostsWithDown / totalHosts * 100).toFixed(1),
                unreachablePct: (hostsWithUnreachable / totalHosts * 100).toFixed(1)
            };
        }
    }
    
    // Helper function to get status colors - same as table cell background colors
    function getStatusColor(status) {
        // Check if we're in dark theme
        const isDarkTheme = document.body.classList.contains('dark-theme') ||
                          getComputedStyle(document.body).backgroundColor.includes('rgb(26, 26, 26)');

        if (isDarkTheme) {
            // Dark theme - use 75% opacity
            switch(status) {
                case 'success':
                case 'ok': return 'rgba(205,224,107,0.75)';  // Green cell background
                case 'warning': return 'rgba(255,165,0,0.75)';  // Orange cell background
                case 'critical': return 'rgba(212,29,40,0.75)';  // Red cell background
                case 'unknown': return 'rgba(100,116,139,0.75)';  // Gray cell background
                default: return '#ccc';
            }
        } else {
            // Light theme - use 75% opacity
            switch(status) {
                case 'success':
                case 'ok': return 'rgba(76,175,80,0.75)';  // Green cell background
                case 'warning': return 'rgba(255,193,7,0.75)';  // Orange cell background
                case 'critical': return 'rgba(244,67,54,0.75)';  // Red cell background
                case 'unknown': return 'rgba(100,116,139,0.75)';  // Gray cell background
                default: return '#ccc';
            }
        }
    }

    // Helper function to prepare Marimekko chart data
    function prepareMarimekkoData(json, type) {
        const data = [];

        if (type === 'services') {
            const services = json?.data?.services || [];
            if (!services.length) return [];

            // Process each service individually - use the EXACT percentages from the table
            services.forEach(svc => {
                const total = (+svc.time_ok || 0) + (+svc.time_warning || 0) + (+svc.time_critical || 0) + (+svc.time_unknown || 0);
                if (total > 0) {
                    const criticalPct = ((+svc.time_critical || 0) / total) * 100;
                    const warningPct = ((+svc.time_warning || 0) / total) * 100;
                    const unknownPct = ((+svc.time_unknown || 0) / total) * 100;
                    const totalIssuesPct = criticalPct + warningPct + unknownPct;

                    // Only include services that have issues
                    if (totalIssuesPct > 0) {
                        const serviceData = {
                            name: `${svc.host_name}/${svc.description}`,
                            hostName: svc.host_name,
                            serviceName: svc.description,
                            scrollId: `service-${svc.host_name}-${svc.description}`.replace(/[^a-zA-Z0-9-]/g, '-'),
                            totalIssues: totalIssuesPct,
                            sections: []
                        };

                        if (criticalPct > 0) {
                            serviceData.sections.push({
                                label: 'Critical',
                                value: criticalPct,
                                color: getStatusColor('critical'),
                                parent: serviceData
                            });
                        }
                        if (warningPct > 0) {
                            serviceData.sections.push({
                                label: 'Warning',
                                value: warningPct,
                                color: getStatusColor('warning'),
                                parent: serviceData
                            });
                        }
                        if (unknownPct > 0) {
                            serviceData.sections.push({
                                label: 'Unknown',
                                value: unknownPct,
                                color: getStatusColor('unknown'),
                                parent: serviceData
                            });
                        }

                        data.push(serviceData);
                    }
                }
            });
        } else {
            // For hosts report - use EXACT percentages from the table
            const hostgroups = json?.data?.hostgroups || [];
            const grpArr = Array.isArray(hostgroups) && hostgroups.length > 0 ? hostgroups : (json?.data?.hostgroup ? [json.data.hostgroup] : []);

            grpArr.forEach(g => {
                const hostList = g.hosts || [];
                hostList.forEach(h => {
                    const total = (+h.time_up || 0) + (+h.time_down || 0) + (+h.time_unreachable || 0);
                    if (total > 0) {
                        const downPct = ((+h.time_down || 0) / total) * 100;
                        const unreachablePct = ((+h.time_unreachable || 0) / total) * 100;
                        const totalIssuesPct = downPct + unreachablePct;

                        // Only include hosts that have issues
                        if (totalIssuesPct > 0) {
                            const hostData = {
                                name: h.name,
                                scrollId: `host-${h.name}`.replace(/[^a-zA-Z0-9-]/g, '-'),
                                totalIssues: totalIssuesPct,
                                sections: []
                            };

                            if (downPct > 0) {
                                hostData.sections.push({
                                    label: 'Down',
                                    value: downPct,
                                    color: getStatusColor('critical'),
                                    parent: hostData
                                });
                            }
                            if (unreachablePct > 0) {
                                hostData.sections.push({
                                    label: 'Unreachable',
                                    value: unreachablePct,
                                    color: getStatusColor('unknown'),
                                    parent: hostData
                                });
                            }

                            data.push(hostData);
                        }
                    }
                });
            });
        }

        // Sort by total issues percentage (descending)
        data.sort((a, b) => b.totalIssues - a.totalIssues);

        return data;
    }

    // Main generator
    async function generateReport() {
        const type = window.reportsUI.getSelectedType(); // hostgroups | services
        const startTs = Math.floor(new Date(window.reportsUI.getStartInput().value).getTime() / 1000);
        const endTs   = Math.floor(new Date(window.reportsUI.getEndInput().value).getTime() / 1000);
        if (isNaN(startTs) || isNaN(endTs) || endTs <= startTs) {
            alert('Invalid time range');
            return;
        }

        let url = `/nagios/cgi-bin/archivejson.cgi?query=availability&availabilityobjecttype=${encodeURIComponent(type)}&starttime=${startTs}&endtime=${endTs}`;
        const entityVal = window.reportsUI.getHostgroupValue();
        if (entityVal && entityVal !== 'all') {
            if (type === 'services') {
                url += `&hostname=${encodeURIComponent(entityVal)}`;
            } else {
                url += `&hostgroup=${encodeURIComponent(entityVal)}`;
            }
        }

        // Loading indicator
        window.reportsUI.showLoading();

        try {
            // Fetch availability data and host IP map in parallel for efficiency
            const [resp, hostIpMap] = await Promise.all([
                fetch(url, { credentials: 'include' }),
                fetchHostIpMap()
            ]);
            if (!resp.ok) throw new Error(resp.statusText);
            const data = await resp.json();
            
            // Store the original data for PDF summary calculation
            window.lastReportData = data;
            
            window.reportsRenderer.renderTable(data, type, hostIpMap);
            window.reportsFilters.applyStatusFilter();
        } catch (err) {
            console.error('Report fetch error', err);
            window.reportsUI.showError('Error fetching report data');
        }
    }

    // Export functions to global scope
    window.reportsCore = {
        formatDuration,
        toLocalIso,
        fetchHostIpMap,
        calculateSummary,
        getStatusColor,
        prepareMarimekkoData,
        generateReport
    };
})(); 