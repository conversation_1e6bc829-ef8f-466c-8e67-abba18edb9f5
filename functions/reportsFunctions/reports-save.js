(function() {
    let savedReports = [];

    // ---------------- Save Report to Server -----------------------
    async function saveReport() {
        // Ensure there is content to save
        const contentDiv = document.getElementById('reports-content');
        if (!contentDiv.querySelector('table')) {
            alert('Nothing to save');
            return;
        }

        const type = window.reportsUI.getSelectedType();
        const startTs = Math.floor(new Date(window.reportsUI.getStartInput().value).getTime() / 1000);
        const endTs = Math.floor(new Date(window.reportsUI.getEndInput().value).getTime() / 1000);
        
        if (isNaN(startTs) || isNaN(endTs) || endTs <= startTs) {
            alert('Invalid time range');
            return;
        }

        // Show the name input modal
        const saveReportNameModal = document.getElementById('saveReportNameModal');
        const reportNameInput = document.getElementById('report-name');
        const saveReportForm = document.getElementById('save-report-form');
        
        if (saveReportNameModal) {
            reportNameInput.value = '';
            saveReportNameModal.style.display = 'flex';
            reportNameInput.focus();
        }
    }

    // Handle save report form submission
    async function handleSaveReportForm(customName = '') {
        const type = window.reportsUI.getSelectedType();
        const startTs = Math.floor(new Date(window.reportsUI.getStartInput().value).getTime() / 1000);
        const endTs = Math.floor(new Date(window.reportsUI.getEndInput().value).getTime() / 1000);
        
        if (isNaN(startTs) || isNaN(endTs) || endTs <= startTs) {
            alert('Invalid time range');
            return;
        }

        // Show loading state
        const saveReportConfirm = document.getElementById('save-report-confirm');
        const originalContent = saveReportConfirm.innerHTML;
        saveReportConfirm.disabled = true;
        saveReportConfirm.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Saving...';

        try {
            const params = new URLSearchParams();
            params.append('type', type);
            params.append('startTs', startTs);
            params.append('endTs', endTs);
            params.append('hostStatuses', Array.from(document.querySelectorAll('#reports-status-filters .reports-status-filter.active')).map(btn => btn.dataset.statusKey).join(','));
            params.append('svcStatuses', Array.from(document.querySelectorAll('#reports-status-filters .reports-status-filter.active')).map(btn => btn.dataset.statusKey).join(','));
            params.append('hostgroup', window.reportsUI.getHostgroupValue());
            if (customName) {
                params.append('customName', customName);
            }

            const response = await fetch('functions/reportsFunctions/saveReport.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: params.toString()
            });

            const result = await response.json();
            
            if (result.success) {
                // Close the modal
                const saveReportNameModal = document.getElementById('saveReportNameModal');
                if (saveReportNameModal) {
                    saveReportNameModal.style.display = 'none';
                }
                
                // Refresh saved reports list if it exists
                if (typeof loadSavedReports === 'function') {
                    loadSavedReports();
                }
            } else {
                alert('Failed to save report: ' + (result.message || 'Unknown error'));
            }
        } catch (error) {
            console.error('Save report error:', error);
            alert('Error saving report');
        } finally {
            // Restore button state
            saveReportConfirm.disabled = false;
            saveReportConfirm.innerHTML = originalContent;
        }
    }

    // ---------------- Saved Reports Management -----------------------
    async function loadSavedReports() {
        try {
            const response = await fetch('functions/reportsFunctions/listSavedReports.php');
            const result = await response.json();
            
            if (result.success) {
                savedReports = result.reports || [];
                displaySavedReports();
                updateSavedReportsBadge();
            }
        } catch (error) {
            console.error('Failed to load saved reports:', error);
        }
    }

    function updateSavedReportsBadge() {
        const badge = document.getElementById('saved-reports-badge');
        if (badge) {
            const count = savedReports.length;
            if (count > 0) {
                badge.textContent = count > 99 ? '99+' : count.toString();
                badge.style.display = 'flex';
            } else {
                badge.style.display = 'none';
            }
        }
    }

    function displaySavedReports() {
        const savedReportsContainer = document.getElementById('saved-reports-container');
        if (!savedReportsContainer) return;

        if (savedReports.length === 0) {
            savedReportsContainer.innerHTML = '<div class="saved-reports-empty">No saved reports found</div>';
            return;
        }

        let html = `
            <div class="saved-reports-header">
                <div class="saved-reports-title">Reports (${savedReports.length})</div>
                <div class="saved-reports-controls">
                    <label class="select-all-checkbox">
                        <input type="checkbox" id="select-all-reports">
                        Select All
                    </label>
                    <button class="bulk-delete-btn" id="bulk-delete-btn" style="display: none;">
                        <i class="fa fa-trash"></i> Delete Selected
                    </button>
                </div>
            </div>
            <div class="saved-reports-list">`;
        
        savedReports.forEach(report => {
            const date = new Date(report.created * 1000);
            const dateStr = date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
            const reportName = report.customName || `${report.type === 'hostgroups' ? 'Hosts' : 'Services'} Report`;
            
            html += `<div class="saved-report-item" data-filename="${report.filename}">
                <div class="report-checkbox">
                    <input type="checkbox" class="report-select-checkbox" data-filename="${report.filename}">
                </div>
                <div class="report-info">
                    <div class="report-title">${reportName}</div>
                    <div class="report-date">${dateStr}</div>
                    <div class="report-details">
                        ${report.rangeDays} day(s) | ${report.fileSizeFormatted}
                        ${report.scheduled ? ' | Scheduled' : ''}
                    </div>
                </div>
                <div class="report-actions">
                    <button class="report-action-btn view-btn" onclick="viewReport('${report.filename}')" title="View">
                        <i class="fa fa-eye"></i>
                    </button>
                    <button class="report-action-btn download-btn" onclick="downloadReport('${report.filename}')" title="Download">
                        <i class="fa fa-download"></i>
                    </button>
                    <button class="report-action-btn delete-btn" onclick="deleteReport('${report.filename}')" title="Delete">
                        <i class="fa fa-trash"></i>
                    </button>
                </div>
            </div>`;
        });
        html += '</div>';
        savedReportsContainer.innerHTML = html;

        // Add event listeners for multi-select functionality
        setupMultiSelectHandlers();
    }

    // Global functions for report actions
    window.viewReport = function(filename) {
        window.open('functions/reportsFunctions/downloadReport.php?file=' + encodeURIComponent(filename) + '&action=view', '_blank');
    };

    window.downloadReport = function(filename) {
        window.open('functions/reportsFunctions/downloadReport.php?file=' + encodeURIComponent(filename) + '&action=download', '_blank');
    };

    window.deleteReport = async function(filename) {
        if (!confirm('Are you sure you want to delete this report?')) return;

        try {
            const response = await fetch('functions/reportsFunctions/deleteReport.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: 'file=' + encodeURIComponent(filename)
            });

            const result = await response.json();
            
            if (result.success) {
                // Refresh the list
                loadSavedReports();
                // Update badge count
                updateSavedReportsBadge();
            } else {
                alert('Failed to delete report: ' + (result.message || 'Unknown error'));
            }
        } catch (error) {
            console.error('Delete report error:', error);
            alert('Error deleting report');
        }
    };

    // Multi-select functionality
    function setupMultiSelectHandlers() {
        const selectAllCheckbox = document.getElementById('select-all-reports');
        const bulkDeleteBtn = document.getElementById('bulk-delete-btn');
        const reportCheckboxes = document.querySelectorAll('.report-select-checkbox');

        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                const isChecked = this.checked;
                reportCheckboxes.forEach(checkbox => {
                    checkbox.checked = isChecked;
                    updateReportItemSelection(checkbox);
                });
                updateBulkDeleteButton();
            });
        }

        reportCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                updateReportItemSelection(this);
                updateSelectAllCheckbox();
                updateBulkDeleteButton();
            });
        });

        if (bulkDeleteBtn) {
            bulkDeleteBtn.addEventListener('click', bulkDeleteReports);
        }
    }

    function updateReportItemSelection(checkbox) {
        const reportItem = checkbox.closest('.saved-report-item');
        if (checkbox.checked) {
            reportItem.classList.add('selected');
        } else {
            reportItem.classList.remove('selected');
        }
    }

    function updateSelectAllCheckbox() {
        const selectAllCheckbox = document.getElementById('select-all-reports');
        const reportCheckboxes = document.querySelectorAll('.report-select-checkbox');
        const checkedCount = document.querySelectorAll('.report-select-checkbox:checked').length;
        
        if (selectAllCheckbox) {
            selectAllCheckbox.checked = checkedCount === reportCheckboxes.length && reportCheckboxes.length > 0;
            selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < reportCheckboxes.length;
        }
    }

    function updateBulkDeleteButton() {
        const bulkDeleteBtn = document.getElementById('bulk-delete-btn');
        const checkedCount = document.querySelectorAll('.report-select-checkbox:checked').length;
        
        if (bulkDeleteBtn) {
            if (checkedCount > 0) {
                bulkDeleteBtn.style.display = 'inline-block';
                bulkDeleteBtn.textContent = `Delete Selected (${checkedCount})`;
            } else {
                bulkDeleteBtn.style.display = 'none';
            }
        }
    }

    async function bulkDeleteReports() {
        const selectedCheckboxes = document.querySelectorAll('.report-select-checkbox:checked');
        if (selectedCheckboxes.length === 0) return;

        const filenames = Array.from(selectedCheckboxes).map(cb => cb.dataset.filename);
        const confirmMessage = `Are you sure you want to delete ${filenames.length} report(s)?`;
        
        if (!confirm(confirmMessage)) return;

        try {
            const response = await fetch('functions/reportsFunctions/deleteMultipleReports.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: 'files=' + encodeURIComponent(JSON.stringify(filenames))
            });

            const result = await response.json();
            
            if (result.success) {
                // Refresh the list
                loadSavedReports();
                // Update badge count
                updateSavedReportsBadge();
            } else {
                alert('Failed to delete reports: ' + (result.message || 'Unknown error'));
            }
        } catch (error) {
            console.error('Bulk delete error:', error);
            alert('Error deleting reports');
        }
    }

    // Function to open saved reports modal
    function openSavedReportsModal() {
        const savedReportsModal = document.getElementById('savedReportsModal');
        if (!savedReportsModal) return;
        
        savedReportsModal.style.display = 'flex';
        // Refresh the saved reports list when opening
        loadSavedReports();
    }

    // Initialize saved reports on page load
    function init() {
        loadSavedReports();
    }

    // Export to global scope
    window.reportsSave = {
        init,
        saveReport,
        handleSaveReportForm,
        loadSavedReports,
        displaySavedReports,
        openSavedReportsModal,
        updateSavedReportsBadge
    };
})(); 