<?php
include "loadenv.php";

class MacAddressRetriever {
    private $host;

    public function __construct($host) {
        $this->host = $host;
    }

    // Check if IP is valid
    public function isValidIp() {
        return filter_var($this->host, FILTER_VALIDATE_IP) !== false;
    }

    // Check if SPM module is available
    public function isSpmAvailable() {
        $spm_check = shell_exec("php " . __DIR__ . "/checkmods.php spm 2>&1");
        return trim($spm_check) === "true";
    }

    // Get MAC address using nmap -sn
    public function getMacAddressWithNmap() {
        $command = "nmap -sn " . escapeshellarg($this->host) . " 2>&1";
        $output = [];
        exec($command, $output, $exitCode);
        $result = implode("\n", $output);

        // Look for MAC address pattern in the output
        if (preg_match('/MAC Address: ([0-9A-Fa-f:]{17})/', $result, $matches)) {
            return $matches[1];
        }

        return false;
    }

    // Get MAC address from Netdisco database
    public function getMacAddressFromNetdisco() {
        // Simple query to get MAC address for the given IP
        $sql = "SELECT mac FROM node_ip WHERE ip = '{$this->host}' AND active = true LIMIT 1;";

        // Execute the query via psql using the same format as get_spm_data.php
        // Use the sudoers configuration: apache ALL=(netdisco) NOPASSWD: /usr/bin/psql -U netdisco -d netdisco *
        $command = "sudo -u netdisco /usr/bin/psql -U netdisco -d netdisco -t -A -c " . escapeshellarg($sql);
        $output = shell_exec($command . ' 2>&1');
        
        if ($output === null) {
            return false;
        }
        
        // Parse the output to get MAC address
        $lines = array_filter(explode("\n", trim($output)));
        
        foreach ($lines as $line) {
            $line = trim($line);
            if ($line === '') {
                continue;
            }
            
            // Validate MAC address format
            if (preg_match('/^([0-9A-Fa-f:]{17})$/', $line)) {
                return $line;
            }
        }
        
        return false;
    }

    // Main function to get MAC address
    public function getMacAddress() {
        if (!$this->isValidIp()) {
            return "Error: Invalid IP address";
        }

        // First try nmap
        $macAddress = $this->getMacAddressWithNmap();
        if ($macAddress) {
            return $macAddress;
        }

        // Check if SPM module is available before trying Netdisco
        if ($this->isSpmAvailable()) {
            // If nmap didn't work and SPM is available, try Netdisco database
            $macAddress = $this->getMacAddressFromNetdisco();
            if ($macAddress) {
                return $macAddress;
            }
        }

        return "Error: MAC address not found";
    }
}

// Handle HTTP requests
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['ip'])) {
    $host = $_GET['ip'];
    $retriever = new MacAddressRetriever($host);
    $result = $retriever->getMacAddress();
    
    // Set proper content type for HTTP response
    header('Content-Type: text/plain; charset=utf-8');
    echo $result;
    exit;
}

// Handle command line usage
if (php_sapi_name() === 'cli') {
    if ($argc < 2) {
        echo "✗ No host provided\n• Usage: php getmacaddress.php <ip_address>\n• Example: php getmacaddress.php ***********\n";
        exit(1);
    }

    $host = $argv[1];
    $retriever = new MacAddressRetriever($host);
    echo $retriever->getMacAddress() . "\n";
}
?>
