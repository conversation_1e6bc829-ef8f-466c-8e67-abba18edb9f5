<?php
include 'language_selector.php';
include 'theme_loader.php'; // Include the theme loader
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Blesk - Network Map</title>
    <link rel="stylesheet" href="styles/<?php echo $chosenTheme; ?>/style.css">
    <link rel="stylesheet" href="styles/<?php echo $chosenTheme; ?>/networkmap.css">
    <link rel="stylesheet" href="../css/font-awesome.min.css">

    <script src="functions/translator.js"></script>
    <script src="functions/translationObserver.js"></script>
    <script src="functions/scanModal.js"></script>
    <script src="functions/networkmapFunctions/iframeHandler.js"></script>
</head>

<body>
    <header>
        <div style="display: flex; align-items: center; gap: 15px;">
            <a href="../../index.php">
                <img style="width: 25px;" src="../../images/blesk-favicon.svg" alt="Home">
            </a>
            <div class="breadcrumbs">
                <a href="infra.php<?php echo isset($_GET['subnet']) ? '?subnet=true' : ''; ?>">
                    <i class="fa fa-home"></i> Home
                </a>
                <span class="separator">/</span>
                <span class="current">
                    <i class="fa fa-map"></i> Network Map
                </span>
            </div>
        </div>
        
        <div class="header-content">
            <div class="hamburger">
                <div class="hamburger-line"></div>
                <div class="hamburger-line"></div>
                <div class="hamburger-line"></div>
            </div>
            <div class="header-buttons">
                <div class="header-dropdown">
                    <a href="#" class="header-button" id="viewDropdownBtn"><i class="fa fa-eye"></i> View <i class="fa fa-caret-down"></i></a>
                    <div class="header-dropdown-content" id="viewDropdownContent">
                        <a class="header-button" href="https://<?php echo $_SERVER['HTTP_HOST']; ?>/bubblemaps/infra.php"><i class="fa fa-th"></i> Bubble View</a>
                        <a class="header-button" href="hostlist.php"><i class="fa fa-list"></i> List View</a>
                    </div>
                </div>
                <div class="header-dropdown">
                    <a href="#" class="header-button" id="menuDropdownBtn"><i class="fa fa-bars"></i> Control Panel <i class="fa fa-caret-down"></i></a>
                    <div class="header-dropdown-content" id="menuDropdownContent">
                        <a class="header-button" id="formModal-button"><i class="fa fa-search"></i> Scan</a>
                        <a class="header-button" onclick='showModal("https://<?php echo $_SERVER["HTTP_HOST"]; ?>/bubblemaps/credentials.php")'><i class="fa fa-key"></i> Credentials</a>
                        <a class="header-button" href="reports.php"><i class="fa fa-file-text"></i> Reports</a>
                    </div>
                </div>
                <a class="header-button" id="openSettingsBtn"><i class="fa fa-cog"></i> Advanced Settings</a>
            </div>
        </div>
    </header>
    
    <?php include "settingsModal.php"; ?>
    
    <!-- iframe Modal -->
    <div id="infoModal" class="iframeModal">
        <div class="iframeModal-content" id="iframeModal-content">
            <span class="iframeMclose">×</span>
            <div class="iframe-loader"></div>
            <iframe id="modal-frame"></iframe>
        </div>
    </div>
    
    <div class="networkmap-container">
        <iframe 
            class="networkmap-iframe" 
            src="https://<?php echo $_SERVER['HTTP_HOST']; ?>/nagvis/frontend/nagvis-js/index.php"
            title="Network Map"
            allowfullscreen>
        </iframe>
    </div>
    
    <script>
        const urlParams = new URLSearchParams(window.location.search);
        const dictionaries = <?php echo json_encode($dictionaries); ?>;
        const selectedLang = '<?php echo $selectedLang; ?>';
        
        // Initialize the translator
        const translator = new Translator(dictionaries, selectedLang);
        translator.init();
        initTranslationObserver(translator);
        
        // Initialize scan modal for networkmap page
        window.scanModal = new ScanModal({
            modalId: 'formModal',
            hasAzureSupport: true,
            hasScanTypeSelector: true,
            hasInternalCheckbox: true,
            ipLabel: 'Enter a single ip, ip range or url.',
            forceScan: true
        });
        
        // Handle hamburger menu
        const hamburger = document.querySelector('.hamburger');
        const headerButtons = document.querySelector('.header-buttons');

        hamburger.addEventListener('click', function(e) {
            e.stopPropagation();
            this.classList.toggle('active');
            headerButtons.classList.toggle('active');
        });

        document.addEventListener('click', function(event) {
            if (!event.target.closest('.header-content')) {
                hamburger.classList.remove('active');
                headerButtons.classList.remove('active');
            }
        });
        
        // Handle View dropdown
        const viewDropdownBtn = document.getElementById('viewDropdownBtn');
        const viewDropdownContent = document.getElementById('viewDropdownContent');
        if(viewDropdownBtn && viewDropdownContent){
            viewDropdownBtn.addEventListener('click', function(e){
                e.preventDefault();
                e.stopPropagation();
                // Close other dropdowns first
                const menuDropdownContent = document.getElementById('menuDropdownContent');
                if(menuDropdownContent) menuDropdownContent.classList.remove('show');
                viewDropdownContent.classList.toggle('show');
            });
        }
        
        // Handle Control Panel dropdown
        const menuDropdownBtn = document.getElementById('menuDropdownBtn');
        const menuDropdownContent = document.getElementById('menuDropdownContent');
        if(menuDropdownBtn && menuDropdownContent){
            menuDropdownBtn.addEventListener('click', function(e){
                e.preventDefault();
                e.stopPropagation();
                // Close other dropdowns first
                if(viewDropdownContent) viewDropdownContent.classList.remove('show');
                menuDropdownContent.classList.toggle('show');
            });
        }
        
        // Close dropdowns when clicking outside
        document.addEventListener('click', function(e){
            if(!e.target.closest('.header-dropdown')){
                if(viewDropdownContent) viewDropdownContent.classList.remove('show');
                if(menuDropdownContent) menuDropdownContent.classList.remove('show');
            }
        });
        
        // Show modal function
        function showModal(url) {
            const modal = document.getElementById('infoModal');
            const iframe = document.getElementById('modal-frame');
            const modalBody = document.getElementById('iframeModal-content');

            if (url.includes('credentials.php')){
                modalBody.style.maxHeight = '800px';
                modalBody.style.maxWidth = '1000px';
                modalBody.style.top = 'calc(50vh - 400px)';
            } else {
                // Explicitly reset all styles for non-credentials modals
                modalBody.style.maxHeight = '';
                modalBody.style.maxWidth = '';
                modalBody.style.top = '';
                modalBody.style.width = '';
                modalBody.style.height = '';
            }

            modal.classList.remove('loaded');
            iframe.style.display = 'none';
            iframe.src = url;
            modal.style.display = 'block';
            modal.classList.add('show');
            modal.classList.remove('small');

            iframe.onload = function() {
                const iframeDocument = iframe.contentDocument || iframe.contentWindow.document;
                const navElement = iframeDocument.querySelector("nav");
                if (navElement) {
                    navElement.style.display = "none";
                }
                
                // Translate the iframe content
                if (typeof translator !== 'undefined') {
                    translator.translateIframe(iframe);
                }
                modal.classList.add('loaded');
                iframe.style.display = 'block';
            };
        }
        
        // Close iframe modal when clicking the close button
        const closeButton = document.querySelector('.iframeMclose');
        if (closeButton) {
            closeButton.addEventListener('click', function() {
                const modal = document.getElementById('infoModal');
                modal.classList.remove('show');
                modal.style.display = 'none';
            });
        }
        
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            const modal = document.getElementById('infoModal');
            if (event.target == modal) {
                modal.classList.remove('show');
                modal.style.display = 'none';
            }
        });
    </script>
    
</body>
</html>
