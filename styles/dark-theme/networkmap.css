/* Network Map Styles - Dark Theme */

/* Network Map Container */
.networkmap-container {
    position: fixed;
    top: 60px; /* Height of header */
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: calc(100vh - 60px);
    background-color: #1a1a1a;
}

.networkmap-iframe {
    width: 100%;
    height: 100%;
    border: none;
    display: block;
    background-color: #1a1a1a;
}

/* Ensure header stays on top */
header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: #2a2a2a;
    border-bottom: 1px solid #333;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
    .networkmap-container {
        top: 80px; /* Adjust for mobile header height */
        height: calc(100vh - 80px);
    }
}

/* Header styles matching hostlist */
.hostlist-status-filters-header {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-left: 15px;
    align-items: center;
}

/* Auto-refresh countdown styles */
.refresh-countdown {
    display: flex;
    align-items: center;
    margin-left: 15px;
    font-size: 14px;
    color: #aaa;
    gap: 5px;
    border-left: 1px solid rgba(255, 255, 255, 0.2);
    padding-left: 10px;
}

.refresh-countdown i {
    color: #aaa;
}

.refresh-countdown.refreshing i {
    animation: spin 1s linear infinite;
}

/* Prevent non-refresh icons inside the countdown from spinning */
.refresh-countdown.refreshing i:not(.fa-refresh) {
    animation: none;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.hostlist-filter-section-header {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-right: 15px;
}

.hostlist-filter-section-header .filter-label {
    font-size: 12px;
    color: #ffffff;
    margin-right: 8px;
    font-weight: 600;
}

/* Host Status Indicators */
.hostlist-status-indicators {
    display: flex;
    background-color: transparent;
    padding: 0;
    gap: 10px;
    align-items: center; /* Ensures vertical alignment with other header items */
    flex-wrap: wrap;
    margin-left: 15px; 
    height: 26px; /* Match height of filter buttons for alignment */
}

.status-indicator {
    position: relative; 
    display: flex;
    align-items: center;
    justify-content: center; 
    width: 26px; 
    height: 26px; 
    border-radius: 50%; 
    cursor: pointer;
    transition: background-color 0.2s, box-shadow 0.2s;
    background-color: rgba(255,255,255,0.08);
    box-shadow: 0 0 0 1px rgba(255,255,255,0.15); 
}

.status-indicator:hover {
    background-color: rgba(255,255,255,0.18);
    box-shadow: 0 0 0 1px rgba(255,255,255,0.25);
}

.status-indicator i {
    color: #ccc; 
    font-size: 13px; 
    line-height: 26px; 
}

/* Badge Styles */
.indicator-badge {
    position: absolute;
    top: 0px;  /* Position badge at the very top of the icon */
    right: 0px; /* Position badge at the very right of the icon */
    transform: translate(40%, -40%); /* Nudge badge slightly outside and up */
    background-color: #F44336; 
    color: white;
    border-radius: 50%;
    font-size: 9px; 
    font-weight: bold;
    display: flex; /* For centering text in small circle */
    align-items: center;
    justify-content: center;
    width: 15px; /* Fixed small width */
    height: 15px; /* Fixed small height */
    text-align: center;
    display: none; /* Hidden by default */
    border: 1px solid #222222; /* Match header bg for badge border in dark theme */
    box-sizing: border-box; /* Include padding and border in element's total width and height */
}

.status-indicator.active i {
    color: #cde06b; /* Green for active - dark theme */
}
.status-indicator.active .indicator-badge {
    display: none; 
}

.status-indicator.inactive i {
    color: #d41d28; /* Red for inactive - dark theme */
}
.status-indicator.inactive .indicator-badge {
    background-color: #d41d28;
    display: flex; /* Use flex to show */
}

.status-indicator.warning i {
    color: #ffa500; /* Yellow for warning - dark theme */
}
.status-indicator.warning .indicator-badge {
    background-color: #ffa500;
    color: #333; 
    display: flex; /* Use flex to show */
}

/* Host Status Popover (dark theme already has dark background) */
.status-hosts-popover {
    position: absolute;
    background-color: #2c2c2c;
    color: #f0f0f0;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.5);
    padding: 8px 12px;
    min-width: 200px;
    max-width: 300px;
    max-height: 300px !important;
    overflow-y: auto;
    z-index: 9999;
    display: none;
    border: 1px solid #444;
}

/* Custom scrollbar for status hosts popover */
.status-hosts-popover::-webkit-scrollbar {
    width: 6px;
}

.status-hosts-popover::-webkit-scrollbar-track {
    background: #333;
    border-radius: 3px;
}

.status-hosts-popover::-webkit-scrollbar-thumb {
    background: #555;
    border-radius: 3px;
    transition: background 0.2s ease;
}

.status-hosts-popover::-webkit-scrollbar-thumb:hover {
    background: #666;
}

.status-hosts-popover::-webkit-scrollbar-thumb:active {
    background: #777;
}

.status-hosts-popover h4 {
    margin: 0 0 8px 0;
    padding-bottom: 6px;
    border-bottom: 1px solid #444;
    font-size: 13px;
    color: #e0e0e0;
    text-align: center;
    padding-right: 20px; /* Make room for close button */
}

.status-hosts-popover ul {
    list-style-type: none;
    padding: 0;
    margin: 0;
}

.status-hosts-popover li {
    padding: 8px 10px;
    border-radius: 3px;
    cursor: pointer;
    transition: background-color 0.15s ease, color 0.15s ease;
    font-size: 12px;
    color: #d0d0d0;
    margin-bottom: 2px;
    border-bottom: 1px solid rgba(255,255,255,0.05);
}

.status-hosts-popover li:last-child {
    border-bottom: none;
}

.status-hosts-popover li:hover {
    background-color: #3c3c3c;
    color: #ffffff;
}

.status-hosts-popover .close-popover {
    position: absolute;
    top: 6px;
    right: 8px;
    cursor: pointer;
    color: #999;
    font-size: 16px;
    width: 20px;
    height: 20px;
    text-align: center;
    line-height: 20px;
    border-radius: 50%;
}

.status-hosts-popover .close-popover:hover {
    color: #fff;
    background-color: rgba(255,255,255,0.1);
}

/* Mobile Status Container Styles */
.mobile-status-container {
    display: none; /* Hidden by default, shown via JS */
    flex-direction: column;
    background-color: #2c2c2c;
    padding: 8px 15px;
    border-bottom: 1px solid #333;
    position: relative;
    z-index: 8;
}

/* Only show the mobile status container in mobile view */
@media (max-width: 768px) {
    /* Move things from header to mobile container */
    .mobile-status-container {
        display: flex;
        gap: 10px;
        align-items: center;
        justify-content: center;
        text-align: center;
    }
    
    /* Ensure refresh icon is visible on mobile */
    .refresh-countdown i.fa-refresh {
        display: inline-block !important;
        visibility: visible !important;
        color: #aaaaaa !important;
        opacity: 1 !important;
    }
    
    /* Adjust status filters in mobile container */
    .mobile-status-container .hostlist-status-filters-header {
        margin-left: 0;
        flex-direction: column;
        width: 100%;
        align-items: center;
        justify-content: center;
    }
    
    .mobile-status-container .hostlist-filter-section-header {
        width: 100%;
        justify-content: center;
        margin-right: 0;
        margin-bottom: 5px;
    }
    
    /* Adjust status indicators in mobile container */
    .mobile-status-container .hostlist-status-indicators {
        margin-left: 0;
        margin-top: 5px;
        width: 100%;
        justify-content: center;
    }
    
    /* Make sure header doesn't show these elements on mobile */
    header .hostlist-status-filters-header,
    header .hostlist-status-indicators {
        display: none;
    }
}
