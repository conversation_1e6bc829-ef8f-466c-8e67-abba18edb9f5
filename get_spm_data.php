<?php
header('Content-Type: application/json');

// Check if SPM module is available (like in netdisco_parent_child_sync.php)
$spm_check = shell_exec("php " . __DIR__ . "/checkmods.php spm 2>&1");
if (trim($spm_check) !== "true") {
    http_response_code(500);
    echo json_encode(['error' => 'SPM module is not available']);
    exit;
}

// Get the host IP from the request
$hostIp = $_GET['ip'] ?? '';

if (empty($hostIp)) {
    http_response_code(400);
    echo json_encode(['error' => 'Host IP is required']);
    exit;
}

try {
    // Build the SPM query with proper formatting for psql
    $sql = <<<SQL
SELECT
    CASE 
        WHEN dp.up = 'up' THEN 'Up'
        ELSE 'Down'
    END AS port_status,
    dp.ip AS parent_ip,
    d.name AS parent_name,
    dp.port AS port,
    dp.descr AS port_description,
    dp.speed AS port_speed,
    n.mac AS connected_mac,
    native_vlan_data.vlan AS native_vlan,
    vlan_data.vlan_membership,
    COALESCE(
        (SELECT string_agg(ip::text, ', ') 
         FROM node_ip 
         WHERE mac = n.mac AND active = true
         GROUP BY mac),
        dp.remote_ip::text
    ) AS child_ip,
    COALESCE(d2.name, 'Unknown') AS child_name,
    CASE
        WHEN dp.remote_ip IS NOT NULL THEN 'Uplink'
        WHEN n.mac IS NOT NULL THEN 'Device'
        ELSE 'Unknown'
    END AS connection_type,
    n.time_last AS last_seen
FROM 
    device_port dp
JOIN
    device d ON dp.ip = d.ip

-- Only include node data seen in last hour
LEFT JOIN 
    node n ON dp.ip = n.switch AND dp.port = n.port AND n.time_last > NOW() - INTERVAL '1 hour'

LEFT JOIN
    device d2 ON n.mac = d2.mac

-- VLAN membership (all VLANs per port)
LEFT JOIN (
    SELECT
        ip,
        port,
        string_agg(vlan::text, ',' ORDER BY vlan) AS vlan_membership
    FROM
        device_port_vlan
    GROUP BY
        ip, port
) vlan_data ON dp.ip = vlan_data.ip AND dp.port = vlan_data.port

-- Native VLAN(s): aggregate if multiple exist
LEFT JOIN (
    SELECT
        ip,
        port,
        string_agg(vlan::text, ',' ORDER BY vlan) AS vlan
    FROM
        device_port_vlan
    WHERE native = true OR egress_tag = false
    GROUP BY ip, port
) native_vlan_data ON dp.ip = native_vlan_data.ip AND dp.port = native_vlan_data.port

-- No filter on dp.up — includes both up and down ports
ORDER BY 
    dp.ip, dp.port;
SQL;

    // Execute the query via psql with proper formatting options
    // Use the sudoers configuration: apache ALL=(netdisco) NOPASSWD: /usr/bin/psql -U netdisco -d netdisco *
    $command = "sudo -u netdisco /usr/bin/psql -U netdisco -d netdisco -t -A -F'|' -c " . escapeshellarg($sql);
    
    $output = shell_exec($command . ' 2>&1');
    
    if ($output === null) {
        throw new Exception('Failed to execute database query');
    }

    // Parse the output - now it's pipe-separated without headers
    $lines = array_filter(explode("\n", trim($output)));
    
    $parentConnections = [];
    $childConnections = [];
    $portGroups = []; // Group connections by port
    
    foreach ($lines as $line) {
        $line = trim($line);
        if ($line === '') continue;
        
        // Parse the pipe-separated line
        $parts = explode('|', $line);
        
        if (count($parts) >= 13) {
            $portStatus = trim($parts[0]);
            $parentIp = trim($parts[1]);
            $parentName = trim($parts[2]);
            $port = trim($parts[3]);
            $portDescription = trim($parts[4]);
            $portSpeed = trim($parts[5]);
            $connectedMac = trim($parts[6]);
            $nativeVlan = trim($parts[7]);
            $vlanMembership = trim($parts[8]);
            $childIp = trim($parts[9]);
            $childName = trim($parts[10]);
            $connectionType = trim($parts[11]);
            $lastSeen = trim($parts[12]);
            
            // Clean up IP addresses (remove /32, /24, etc.) and handle multiple IPs
            $cleanChildIps = [];
            $ipList = explode(',', $childIp);
            foreach ($ipList as $ip) {
                $cleanIp = trim(preg_replace('/\/\d+$/', '', $ip));
                if (!empty($cleanIp)) {
                    $cleanChildIps[] = $cleanIp;
                }
            }
            
            // Create a unique key for grouping by parent_ip and port
            $portKey = $parentIp . '|' . $port;
            
            // Initialize port group if it doesn't exist
            if (!isset($portGroups[$portKey])) {
                $portGroups[$portKey] = [
                    'port_status' => $portStatus,
                    'parent_ip' => $parentIp,
                    'parent_name' => $parentName,
                    'port' => $port,
                    'port_description' => $portDescription,
                    'port_speed' => $portSpeed,
                    'native_vlan' => $nativeVlan,
                    'vlan_membership' => $vlanMembership,
                    'connected_devices' => [],
                    'connection_types' => [],
                    'last_seen_times' => []
                ];
            }
            
            // Add device information to the port group
            if (!empty($connectedMac) || !empty($childIp)) {
                $deviceInfo = [
                    'mac' => $connectedMac,
                    'ips' => $cleanChildIps,
                    'name' => $childName,
                    'connection_type' => $connectionType,
                    'last_seen' => $lastSeen
                ];
                $portGroups[$portKey]['connected_devices'][] = $deviceInfo;
                
                // Track connection types and last seen times
                if (!in_array($connectionType, $portGroups[$portKey]['connection_types'])) {
                    $portGroups[$portKey]['connection_types'][] = $connectionType;
                }
                if (!empty($lastSeen)) {
                    $portGroups[$portKey]['last_seen_times'][] = $lastSeen;
                }
            }
        }
    }
    
    // Convert grouped data back to connection format
    foreach ($portGroups as $portGroup) {
        // Aggregate device information
        $deviceNames = [];
        $deviceIps = [];
        $connectionTypes = $portGroup['connection_types'];
        $lastSeenTimes = $portGroup['last_seen_times'];
        
        foreach ($portGroup['connected_devices'] as $device) {
            if (!empty($device['name']) && $device['name'] !== 'Unknown' && !in_array($device['name'], $deviceNames)) {
                $deviceNames[] = $device['name'];
            }
            foreach ($device['ips'] as $ip) {
                if (!empty($ip) && $ip !== 'N/A' && !in_array($ip, $deviceIps)) {
                    $deviceIps[] = $ip;
                }
            }
        }
        
        $connection = [
            'port_status' => $portGroup['port_status'],
            'parent_ip' => $portGroup['parent_ip'],
            'parent_name' => $portGroup['parent_name'],
            'port' => $portGroup['port'],
            'port_description' => $portGroup['port_description'],
            'port_speed' => $portGroup['port_speed'],
            'native_vlan' => $portGroup['native_vlan'],
            'vlan_membership' => $portGroup['vlan_membership'],
            'child_ip' => !empty($deviceIps) ? implode(', ', $deviceIps) : 'N/A',
            'child_name' => !empty($deviceNames) ? implode(', ', $deviceNames) : 'Unknown',
            'connection_type' => !empty($connectionTypes) ? implode(', ', array_unique($connectionTypes)) : 'Unknown',
            'last_seen' => !empty($lastSeenTimes) ? max($lastSeenTimes) : null // Use most recent time
        ];
        
        // Check if this host is a parent (switch)
        if ($portGroup['parent_ip'] === $hostIp) {
            $parentConnections[] = $connection;
        }
        
        // Check if this host is a child (connected device)
        $isChildConnection = false;
        foreach ($deviceIps as $ip) {
            if ($ip === $hostIp) {
                $isChildConnection = true;
                break;
            }
        }
        
        if ($isChildConnection) {
            $childConnections[] = $connection;
        }
    }
    
    echo json_encode([
        'success' => true,
        'parent_connections' => $parentConnections,
        'child_connections' => $childConnections
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Database query failed: ' . $e->getMessage(),
        'raw_output' => $output ?? 'No output'
    ]);
}
?> 