<div class="container">
    <div class="status-header">
        <!-- Service count moved to filter container -->
    </div>
    <div class="host-availability-container">
        <div id="host-card" class="host-card"></div>
    </div>
    <div class="services-column">
        <!-- Add search bar -->
        <div class="search-container">
            <div class="search-controls">
                <button id="view-toggle-button" class="view-toggle-button" title="Switch to list view">
                    <i class="fa fa-th-large"></i>
                </button>
                <button id="selection-mode-button" class="selection-mode-button" title="Enable selection mode" onclick="toggleSelectionMode()">
                    <i class="fa fa-check-square-o"></i>
                </button>
                <div class="search-wrapper">
                    <i class="fa fa-search search-icon" aria-hidden="true"></i>
                    <input type="text" id="service-search" class="service-search" placeholder="Search..." aria-label="Search">
                    <button id="clear-search" class="clear-search" aria-label="Clear search"><i class="fa fa-times" aria-hidden="true"></i></button>
                </div>
            </div>
            <div class="filter-container">
                <div id="service-count" class="service-count-badge"><i class="fa fa-server" aria-hidden="true"></i> Loading...</div>
                <div class="filter-label">Filter by status:</div>
                <div class="filter-buttons">
                    <button class="filter-btn" data-status="all">All</button>
                    <button class="filter-btn ok-filter" data-status="ok">OK</button>
                    <button class="filter-btn warning-filter" data-status="warning">Warning</button>
                    <button class="filter-btn critical-filter" data-status="critical">Critical</button>
                    <button class="filter-btn unknown-filter" data-status="unknown">Unknown</button>
                    <button class="filter-btn pending-filter" data-status="pending">Pending</button>
                </div>
            </div>
        </div>
        <div id="status" class="status-container">
            <div class="loading">Loading service status...</div>
        </div>
        
        <!-- SPM Section -->
        <div id="spm-container" class="spm-container" style="display: none;">
            <div class="spm-loading">
                <i class="fa fa-spinner fa-spin"></i>
                <p>Loading SPM data...</p>
            </div>
        </div>
    </div>
</div>

<!-- Add AI assistant bubble -->
<div id="ai-assistant-bubble" class="ai-bubble">
    <div class="ai-bubble-content">
        <div class="ai-bubble-header">
            <img src="imgs/icons/ai-avatar.png" alt="AI Assistant" class="ai-avatar">
            <span>AI Assistant</span>
            <button id="close-ai-bubble" class="close-bubble">&times;</button>
        </div>
        <div class="ai-bubble-message">
            I've detected a service with an OID error. This typically indicates a missing SNMP MIB type. Please contact support so we can add the required OID files for proper monitoring.
        </div>
    </div>
</div>

<!-- Rename Service Modal -->
<div id="rename-service-modal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h2 class="modal-title">Rename Service</h2>
            <span id="rename-modal-close" class="modal-close">×</span>
        </div>
        <div class="modal-body">
            <div class="rename-form">
                <div class="modal-row">
                    <label for="current-service-name">Current Name:</label>
                    <input type="text" id="current-service-name" readonly>
                </div>

                <div class="modal-row">
                    <label for="new-service-name">New Name:</label>
                    <input type="text" id="new-service-name" placeholder="Enter new service name">
                </div>

                <div class="modal-row">
                    <div class="rename-buttons">
                        <button id="rename-cancel-btn" class="rename-btn rename-btn-secondary">Cancel</button>
                        <button id="rename-confirm-btn" class="rename-btn rename-btn-primary">Rename</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- PDF Export Modal -->
<div id="pdf-export-modal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h2 class="modal-title">Export Host Availability Report</h2>
            <span id="pdf-export-modal-close" class="modal-close">×</span>
        </div>
        <div class="modal-body">
            <div class="pdf-export-form">
                <div class="modal-row">
                    <label for="pdf-modal-start-date">Start Date:</label>
                    <input type="datetime-local" id="pdf-modal-start-date" title="Start time">
                </div>

                <div class="modal-row">
                    <label for="pdf-modal-end-date">End Date:</label>
                    <input type="datetime-local" id="pdf-modal-end-date" title="End time">
                </div>

                <div class="modal-row">
                    <div class="pdf-export-buttons">
                        <button id="pdf-export-cancel-btn" class="pdf-export-btn-secondary">Cancel</button>
                        <button id="pdf-export-generate-btn" class="pdf-export-btn-primary">
                            <i class="fa fa-file-pdf-o"></i>
                            Generate PDF
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.ai-bubble {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 300px;
    background-color: #ffebee;
    border-radius: var(--radius, 10px);
    box-shadow: var(--shadow, 0 4px 12px rgba(0,0,0,0.15));
    z-index: 1001;
    display: none;
    animation: bubbleIn 0.3s ease-out;
    overflow: hidden;
    border: 1px solid #ffcdd2;
}

@keyframes bubbleIn {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.ai-bubble-content {
    padding: 15px;
}

.ai-bubble-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    border-bottom: 1px solid #ffcdd2;
    padding-bottom: 8px;
}

.ai-avatar {
    width: 65px;
    height: 65px;
    margin-right: 10px;
    filter: var(--avatar-filter, none);
}

.ai-bubble-header span {
    color: #d32f2f;
    font-weight: 600;
}

.close-bubble {
    margin-left: auto;
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #d32f2f;
}

.close-bubble:hover {
    color: #b71c1c;
}

.ai-bubble-message {
    font-size: 14px;
    line-height: 1.4;
    color: #d32f2f;
}


</style>

<div id="custom-context-menu" style="display:none; position:absolute; z-index:1001;"></div>
<div id="service-modal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h2 class="modal-title" id="modal-title"></h2>
            <span id="modal-refresh" class="modal-refresh" style="display: none;"><i class="fa fa-refresh" aria-hidden="true"></i></span>
            <span id="modal-delete" class="modal-delete" style="display: none;"><i class="fa fa-trash"
                    aria-hidden="true"></i></span>
            <span id="modal-options" class="modal-options"><i class="fa fa-ellipsis-v" aria-hidden="true"></i></span>
            <span id="modal-close" class="modal-close">×</span>
        </div>
        <div id="modal-body" class="modal-body"></div>
    </div>
</div>
<script src="functions/hostApmFunctions/helperFunctions.js"></script>
<script src="functions/hostApmFunctions/iframeHandler.js"></script>
<script src="functions/hostApmFunctions/modalHandler.js"></script>
<script src="functions/hostApmFunctions/statusDatHandler.js"></script>
<script src="functions/hostApmFunctions/aiAssistant.js"></script>
<script src="functions/hostApmFunctions/openModals.js"></script>
<script src="functions/hostApmFunctions/contextMenu.js"></script>
<script src="functions/hostApmFunctions/troubleshooting.js"></script>
<script src="functions/hostApmFunctions/nagiosComments.js"></script>
<script src="functions/hostApmFunctions/deleteService.js"></script>
<script src="functions/hostApmFunctions/renameService.js"></script>
<script src="functions/hostApmFunctions/searchServices.js"></script>
<script src="functions/hostApmFunctions/viewToggle.js"></script>
<script src="functions/d3.v7.min.js"></script>
<script src="functions/hostApmFunctions/phpAvailabilityReport.js"></script>
<script src="functions/hostApmFunctions/spmHandler.js"></script>
<script src="functions/hostApmFunctions/hostApm.js"></script>
<script src="functions/helperFunctions.js"></script>